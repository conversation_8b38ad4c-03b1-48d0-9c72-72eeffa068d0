<?xml version="1.0" encoding="UTF-8" ?>
<Wexflow>
  <Setting name="workflowsFolder" value="C:\Wexflow-netcore\Workflows" />
  <Setting name="recordsFolder" value="C:\Wexflow-netcore\Records" />
  <Setting name="recordsHotFolder" value="C:\Wexflow-netcore\Records\_HotFolder" />
  <Setting name="tempFolder" value="C:\Wexflow-netcore\Temp" />
  <Setting name="tasksFolder" value="C:\Wexflow-netcore\Tasks" />
  <Setting name="approvalFolder" value="C:\Wexflow-netcore\Approval" />
  <Setting name="xsd" value="C:\Wexflow-netcore\Workflow.xsd" />
  <Setting name="tasksNamesFile" value="C:\Wexflow-netcore\TasksNames.json" />
  <Setting name="tasksSettingsFile" value="C:\Wexflow-netcore\TasksSettings.json" />
  <Setting name="globalVariablesFile" value="C:\Wexflow-netcore\GlobalVariables.xml" />
  <!-- SQLite or MongoDB or SQLServer or PostgreSQL or MySQL or LiteDB -->
  <Setting name="dbType" value="SQLite" />
  <!-- SQLite -->
  <Setting name="connectionString" value="Data Source=C:\Wexflow-netcore\Database\Wexflow.sqlite;Version=3;" />
  <!-- MongoDB -->
  <!--<Setting name="connectionString" value="Database=wexflow_netcore;MongoUrl=mongodb://localhost:27017;EnabledSslProtocols=false;SslProtocols=None" />-->
  <!-- SQLServer -->
  <!--<Setting name="connectionString" value="Server=localhost;Trusted_Connection=True;Database=wexflow_netcore;" />-->
  <!-- PostgreSQL -->
  <!--<Setting name="connectionString" value="Server=127.0.0.1;User Id=postgres;Password=000000;Database=wexflow_netcore;Port=5432" />-->
  <!-- MySQL -->
  <!--<Setting name="connectionString" value="Server=localhost;Database=wexflow_netcore;Uid=root;Pwd=000000;Port=3306" />-->
  <!-- LiteDB -->
  <!--<Setting name="connectionString" value="Filename=C:\Wexflow-netcore\Database\Wexflow.db; Connection=direct" />-->
</Wexflow>
