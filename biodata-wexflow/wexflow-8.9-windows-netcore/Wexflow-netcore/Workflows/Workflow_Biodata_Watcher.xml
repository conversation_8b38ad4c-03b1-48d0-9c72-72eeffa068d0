<Workflow xmlns="urn:wexflow-schema" id="1001" name="ThemaWatcherBiodata" description="Surveillance et traitement des fichiers PDF pour HEMADIALYSE">
  <Settings>
    <Setting name="cronExpression" value="* * * * *" /> <!-- toutes les minutes -->
  </Settings>
  <Tasks>
    <!-- 1. Détection de fichiers à traiter -->
    <Task id="1" name="FilesLoader" description="Détection de fichiers PDF/JPEG/JPG" enabled="true">
      <Setting name="folder" value="/opt/airflow/data" />
      <Setting name="searchPattern" value="*/bbio/*/*.pdf;*/bbio/*/*.jpeg;*/bbio/*/*.jpg" />
      <Setting name="recursive" value="true" />
    </Task>
    <!-- 2. Traitement métier complexe : API, copie, renommage, suppression, etc. -->
    <Task id="2" name="CustomThemaProcessing" description="Traitement métier complet" enabled="true">
      <Setting name="mountedLabPath" value="/opt/airflow/mounted_lab" />
      <Setting name="hmdApiUrl" value="http://**********:8084/" />
      <Setting name="themaApiUrl" value="http://biodata_api:8066/" />
      <Setting name="jwtToken" value="VOTRE_TOKEN_ICI" />
      <Setting name="identifiantLabo" value="EMA_LAB" />
      <!-- Autres paramètres personnalisés si besoin -->
    </Task>
    <!-- 3. Fin du workflow (optionnel) -->
    <Task id="3" name="ProcessFinished" description="Fin du workflow" enabled="true" />
  </Tasks>
  <ExecutionGraph>
    <Task id="1"><Parent id="-1" /></Task>
    <Task id="2"><Parent id="1" /></Task>
    <Task id="3"><Parent id="2" /></Task>
  </ExecutionGraph>
</Workflow>
