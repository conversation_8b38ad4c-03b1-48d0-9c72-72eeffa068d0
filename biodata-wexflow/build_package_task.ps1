# Build et Deploy des Custom Tasks Wexflow
# Compatible macOS et Windows

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Build et Deploy des Custom Tasks" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Sauvegarde du répertoire courant
$OriginalDir = Get-Location

try {
    # Navigation vers le dossier des custom tasks
    $ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    $CustomTasksDir = Join-Path $ScriptDir "biodata-wexflow\wexflow-lib-custom-task"
    
    Write-Host "Navigation vers: $CustomTasksDir" -ForegroundColor Yellow
    Set-Location $CustomTasksDir

    # Vérification que le fichier solution existe
    if (-not (Test-Path "WEXFLOW.sln")) {
        Write-Host "ERREUR: Fichier solution WEXFLOW.sln non trouvé !" -ForegroundColor Red
        Read-Host "Appuyez sur Entrée pour continuer"
        exit 1
    }

    # Clean et build de la solution
    Write-Host ""
    Write-Host "Nettoyage des anciens builds..." -ForegroundColor Yellow
    $cleanResult = & dotnet clean WEXFLOW.sln
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERREUR lors du nettoyage !" -ForegroundColor Red
        Read-Host "Appuyez sur Entrée pour continuer"
        exit 1
    }

    Write-Host ""
    Write-Host "Compilation en mode Release..." -ForegroundColor Yellow
    $buildResult = & dotnet build WEXFLOW.sln --configuration Release --no-restore
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERREUR lors de la compilation !" -ForegroundColor Red
        Read-Host "Appuyez sur Entrée pour continuer"
        exit 1
    }

    # Création du dossier de destination s'il n'existe pas
    $DestDir = Join-Path $ScriptDir "biodata-wexflow\wexflow-8.9-windows-netcore\Wexflow.Custom"
    if (-not (Test-Path $DestDir)) {
        Write-Host "Création du dossier de destination..." -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $DestDir -Force | Out-Null
    }

    # Copie des DLL compilées
    Write-Host ""
    Write-Host "Copie des tâches personnalisées..." -ForegroundColor Yellow

    $Projects = @(
        @{Name = "Wexflow.Tasks.BioData"; DisplayName = "BioData (BiodataFileWatcher)"},
        @{Name = "Wexflow.Tasks.BiodataJobStatusUpdater"; DisplayName = "BiodataJobStatusUpdater"},
        @{Name = "Wexflow.Tasks.BiodataResultDownloader"; DisplayName = "BiodataResultDownloader"}
    )

    $CopiedCount = 0
    foreach ($Project in $Projects) {
        $SourcePath = Join-Path $Project.Name "bin\Release\net9.0\*.dll"
        
        if (Test-Path $SourcePath) {
            $DllFiles = Get-ChildItem $SourcePath
            if ($DllFiles.Count -gt 0) {
                Copy-Item $SourcePath $DestDir -Force
                Write-Host "- $($Project.DisplayName) copiée ($($DllFiles.Count) fichier(s))" -ForegroundColor Green
                $CopiedCount++
            } else {
                Write-Host "ATTENTION: Aucune DLL trouvée pour $($Project.Name)" -ForegroundColor Yellow
            }
        } else {
            Write-Host "ATTENTION: Dossier de build non trouvé pour $($Project.Name)" -ForegroundColor Yellow
        }
    }

    # Affichage du résumé
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "  Déploiement terminé !" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Résumé:" -ForegroundColor White
    Write-Host "- $CopiedCount projet(s) déployé(s)" -ForegroundColor Green
    Write-Host "- Destination: $DestDir" -ForegroundColor White
    Write-Host ""
    
    # Liste des fichiers copiés
    $CopiedFiles = Get-ChildItem $DestDir -Filter "*.dll" | Sort-Object Name
    if ($CopiedFiles.Count -gt 0) {
        Write-Host "Fichiers déployés:" -ForegroundColor White
        foreach ($File in $CopiedFiles) {
            Write-Host "  - $($File.Name)" -ForegroundColor Gray
        }
    }
    
    Write-Host ""
    Write-Host "Vous pouvez maintenant utiliser vos custom tasks dans Wexflow." -ForegroundColor Green
    Write-Host ""

} catch {
    Write-Host "ERREUR: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    # Retour au répertoire original
    Set-Location $OriginalDir
    Read-Host "Appuyez sur Entrée pour continuer"
}
