<Workflow xmlns="urn:wexflow-schema" id="198" name="Biodata Result Downloader" description="Workflow de téléchargement et conversion des résultats biodata en HL7">
  <Settings>
    <Setting name="launchType" value="cron" />
    <Setting name="cronExpression" value="0 0/5 * * * ?" /> <!-- Every 5 minutes -->
    <Setting name="enabled" value="true" />
    <Setting name="enableParallelJobs" value="false" />
  </Settings>
  <Tasks>
    <!-- 1. BiodataResultDownloader : téléchargement et conversion des résultats -->
    <Task id="1" name="BiodataResultDownloader" description="Télécharge les résultats biodata depuis l'API Thema et les convertit en HL7" enabled="true">
      <Setting name="themaApiUrl" value="https://biodata.hemadialyse.com" />
      <Setting name="hmdApiUrl" value="http://**********:8083/" />
      <Setting name="jwtToken" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJERVYiLCJleHAiOjI2MTkyMjgxODAxLCJpYXQiOjE3NDM3NTY2MDUsImlzcyI6IkhlbWFkaWFseXNlIiwic3ViIjoiIn0.r64OsT1xDjtBCOxlIseTaEiR6G3jW9ER1LIxs2VKq1g" />
      <Setting name="mountedLabPath" value="G:\\hemadialyse\HMD_PJ_BIO\" />
    </Task>
  </Tasks>
  <ExecutionGraph>
    <Task id="1"><Parent id="-1" /></Task>
  </ExecutionGraph> 
</Workflow>
