<?xml version="1.0" encoding="utf-8"?>
<Workflow xmlns="urn:wexflow-schema" id="19198" name="Biodata Watcher" description="Workflow de surveillance et traitement des fichiers biodata 2">
  <Settings>
    <Setting name="launchType" value="trigger" />
    <Setting name="enabled" value="true" />
    <Setting name="approval" value="false" />
    <Setting name="enableParallelJobs" value="false" />
  </Settings>
  <Tasks>
    <!-- 1. BiodataFileWatcher : surveille le dossier avec filtrage par date -->
    <Task id="2" name="BiodataFileWatcher" description="Surveillance du dossier avec filtrage par date d'activation" enabled="true">
      <Setting name="watchPath" value="Z:\" />
      <Setting name="filePattern" value="*.pdf" />
      <Setting name="includeSubfolders" value="true" />
      <Setting name="onFileCreated" value="3" />
      <Setting name="hmd_api_url" value="http://**********:8083" />
      <Setting name="thema_api_url" value="https://biodata.hemadialyse.com" />
      <Setting name="hmd_jwt_token" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJERVYiLCJleHAiOjI2MTkyMjgxODAxLCJpYXQiOjE3NDM3NTY2MDUsImlzcyI6IkhlbWFkaWFseXNlIiwic3ViIjoiIn0.r64OsT1xDjtBCOxlIseTaEiR6G3jW9ER1LIxs2VKq1g" />
      <Setting name="identifiant_labo" value="EMA_LAB" />
      <Setting name="base_path" value="\\**********\hmd_pj\" />
      <Setting name="repert_labo" value="\\**********\hmd_pj_bio\" />
    </Task>

   
  </Tasks>
  <ExecutionGraph>
    <Task id="2"><Parent id="-1" /></Task>

  </ExecutionGraph>
</Workflow>
