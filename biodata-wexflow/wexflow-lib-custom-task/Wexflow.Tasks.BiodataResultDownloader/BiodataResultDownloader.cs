using System.Xml.Linq;
using System.Text.Json;
using Wexflow.Core;
using System.Text;

// ========================================
// BiodataResultDownloader - Téléchargement et conversion des résultats biodata
// ========================================
namespace Wexflow.Tasks.BiodataResultDownloader
{
    /// <summary>
    /// Tâche personnalisée qui télécharge les résultats biodata et les convertit en HL7
    /// Reprend la fonctionnalité du DAG thema_download_biodata avec conversion HL7
    /// </summary>
    public class BiodataResultDownloader : Core.Task
    {
        public string ThemaApiUrl { get; }
        public string HmdApiUrl { get; }
        public string JwtToken { get; }
        public string MountedLabPath { get; }

        private static readonly HttpClient _httpClient = new HttpClient();

        public BiodataResultDownloader(XElement xe, Workflow wf) : base(xe, wf)
        {
            // Enregistrement du fournisseur d'encodage pour Windows-1252
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
            
            // Configuration depuis le XML
            ThemaApiUrl = GetSetting("themaApiUrl") ?? "http://biodata_api:8000/";
            HmdApiUrl = GetSetting("hmdApiUrl") ?? "http://10.1.16.18:8083/";
            JwtToken = GetSetting("jwtToken") ?? "";
            MountedLabPath = GetSetting("mountedLabPath") ?? @"C:\temp\biodata_lab";
        }

        public override Core.TaskStatus Run()
        {
            Info("Démarrage de BiodataResultDownloader...");
            
            try
            {
                Info($"API Thema: {ThemaApiUrl}");
                Info($"API HMD: {HmdApiUrl}");
                Info($"Chemin laboratoire: {MountedLabPath}");

                // Télécharger et traiter les fichiers
                var result = DownloadAndSaveFiles();
                
                if (result.Status == "success")
                {
                    Info($"Téléchargement réussi pour le patient: {result.PatientNumber}");
                    return new Core.TaskStatus(Status.Success, false);
                }
                else if (result.Status == "no_jobs")
                {
                    Info("Aucun job disponible pour téléchargement");
                    return new Core.TaskStatus(Status.Success, false);
                }
                else
                {
                    Error($"Erreur lors du téléchargement: {result.Message}");
                    return new Core.TaskStatus(Status.Warning, false);
                }
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur dans BiodataResultDownloader: {0}", ex.Message);
                return new Core.TaskStatus(Status.Error, false);
            }
        }

        private DownloadResult DownloadAndSaveFiles()
        {
            try
            {
                // Configuration de l'URL de l'API Thema pour le téléchargement
                var baseUrl = ThemaApiUrl.TrimEnd('/');
                var url = $"{baseUrl}/workflow/download-succeeded";
                
                Info($"=== DÉBUT APPEL API ===");
                Info($"URL cible: {url}");
                Info($"Méthode: GET");
                
                // Configuration des en-têtes HTTP pour la requête
                var requestHeaders = new Dictionary<string, string>
                {
                    {"Accept", "application/json"}
                };
                
                Info($"En-têtes de requête:");
                foreach (var header in requestHeaders)
                {
                    Info($"  {header.Key}: {header.Value}");
                }
                
                // Configuration du client HTTP avec les en-têtes
                var request = new HttpRequestMessage(HttpMethod.Get, url);
                foreach (var header in requestHeaders)
                {
                    request.Headers.Add(header.Key, header.Value);
                }
                
                Info($"Envoi de la requête HTTP GET...");
                var startTime = DateTime.Now;
                
                // Exécution de la requête HTTP GET
                var response = _httpClient.SendAsync(request).Result;
                
                var duration = DateTime.Now - startTime;
                Info($"=== RÉPONSE API REÇUE ===");
                Info($"Durée de la requête: {duration.TotalMilliseconds}ms");
                Info($"Code de statut HTTP: {response.StatusCode} ({(int)response.StatusCode})");
                Info($"Phrase de statut: {response.ReasonPhrase}");
                
                // Log des en-têtes de réponse
                Info($"En-têtes de réponse:");
                foreach (var header in response.Headers)
                {
                    Info($"  {header.Key}: {string.Join(", ", header.Value)}");
                }
                foreach (var header in response.Content.Headers)
                {
                    Info($"  {header.Key}: {string.Join(", ", header.Value)}");
                }
                
                // Gestion des différents codes de réponse HTTP
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    Info("=== RÉPONSE 404 - AUCUN JOB DISPONIBLE ===");
                    Info("Aucun job disponible pour téléchargement (erreur 404)");
                    return new DownloadResult
                    {
                        Status = "no_jobs",
                        Message = "No jobs available for download"
                    };
                }
                
                if (!response.IsSuccessStatusCode)
                {
                    Info($"=== RÉPONSE D'ERREUR - CODE {(int)response.StatusCode} ===");
                    var errorContent = response.Content.ReadAsStringAsync().Result;
                    Error($"Contenu de l'erreur (longueur: {errorContent.Length} caractères):");
                    Error($"Contenu brut: '{errorContent}'");
                    Error($"Type de contenu: {response.Content.Headers.ContentType?.MediaType ?? "non spécifié"}");
                    return new DownloadResult
                    {
                        Status = "no_jobs",
                        Message = "No jobs available for download"
                    };
                }
                
                // Lecture du contenu de la réponse
                Info($"=== LECTURE DU CONTENU DE RÉPONSE ===");
                var content = response.Content.ReadAsStringAsync().Result;
                Info($"Taille du contenu: {content.Length} caractères");
                Info($"Type de contenu: {response.Content.Headers.ContentType?.MediaType ?? "non spécifié"}");
                Info($"Encodage: {response.Content.Headers.ContentType?.CharSet ?? "non spécifié"}");
                
                if (content.Length > 0)
                {
                    Info($"Contenu reçu (500 premiers caractères): {content.Substring(0, Math.Min(500, content.Length))}...");
                    if (content.Length > 500)
                    {
                        Info($"... (contenu tronqué, {content.Length - 500} caractères restants)");
                    }
                }
                else
                {
                    Info("ATTENTION: Contenu vide reçu de l'API");
                }
                
                // Tentative de décodage de la réponse JSON
                Info($"=== ANALYSE JSON ===");
                JsonDocument data;
                try
                {
                    if (string.IsNullOrWhiteSpace(content))
                    {
                        Error("ERREUR: Contenu vide ou null, impossible de parser le JSON");
                        throw new InvalidOperationException("Contenu de réponse vide");
                    }
                    
                    Info($"Tentative de parsing JSON du contenu...");
                    data = JsonDocument.Parse(content);
                    Info($"Parsing JSON réussi");
                    
                    // Log de la structure JSON
                    Info($"Structure JSON reçue:");
                    Info($"  Type racine: {data.RootElement.ValueKind}");
                    if (data.RootElement.ValueKind == JsonValueKind.Object)
                    {
                        Info($"  Propriétés disponibles:");
                        foreach (var property in data.RootElement.EnumerateObject())
                        {
                            Info($"    - {property.Name}: {property.Value.ValueKind}");
                        }
                    }
                }
                catch (JsonException ex)
                {
                    Error($"=== ERREUR PARSING JSON ===");
                    ErrorFormat("Erreur lors de l'analyse JSON: {0}", ex.Message);
                    Error($"Position de l'erreur: Ligne {ex.LineNumber}, Position {ex.BytePositionInLine}");
                    Error($"Contenu problématique (premiers 1000 caractères):");
                    Error($"'{content.Substring(0, Math.Min(1000, content.Length))}'");
                    
                    // Analyse du contenu pour diagnostiquer le problème
                    if (content.StartsWith("<"))
                    {
                        Error("DIAGNOSTIC: Le contenu semble être du HTML/XML au lieu de JSON");
                    }
                    else if (content.Contains("<!DOCTYPE"))
                    {
                        Error("DIAGNOSTIC: Le contenu semble être une page HTML");
                    }
                    else if (string.IsNullOrWhiteSpace(content))
                    {
                        Error("DIAGNOSTIC: Le contenu est vide ou ne contient que des espaces");
                    }
                    else
                    {
                        Error("DIAGNOSTIC: Format de contenu non reconnu");
                    }
                    
                    throw new InvalidOperationException("Format de réponse invalide");
                }
                
                // Vérification si aucun job terminé n'est disponible
                Info($"=== ANALYSE DU CONTENU JSON ===");
                if (data.RootElement.TryGetProperty("detail", out var detailElement))
                {
                    var detailValue = detailElement.GetString();
                    Info($"Propriété 'detail' trouvée: '{detailValue}'");
                    
                    if (detailValue == "No succeeded jobs available for download")
                    {
                        Info("Aucun job terminé disponible pour téléchargement");
                        return new DownloadResult
                        {
                            Status = "success",
                            Message = "No jobs to download"
                        };
                    }
                }
                else
                {
                    Info("Propriété 'detail' non trouvée dans la réponse");
                }
                
                // Extraction des métadonnées du job depuis la réponse
                Info($"Recherche des métadonnées dans la réponse...");
                if (!data.RootElement.TryGetProperty("metadata", out var metadataElement))
                {
                    Error("ERREUR: Propriété 'metadata' manquante dans la réponse JSON");
                    Error("Propriétés disponibles à la racine:");
                    foreach (var prop in data.RootElement.EnumerateObject())
                    {
                        Error($"  - {prop.Name}");
                    }
                    throw new InvalidOperationException("Métadonnées manquantes dans la réponse");
                }
                
                Info($"Métadonnées trouvées, type: {metadataElement.ValueKind}");
                
                // Extraction des valeurs des métadonnées
                Info($"Extraction des valeurs des métadonnées...");
                var patientNumber = metadataElement.TryGetProperty("patient_number", out var patientElement) 
                    ? patientElement.GetString() : null;
                var fileUuid = metadataElement.TryGetProperty("uuid", out var uuidElement) 
                    ? uuidElement.GetString() : null;
                
                Info($"Métadonnées extraites:");
                Info($"  patient_number: '{patientNumber ?? "NULL"}'");
                Info($"  uuid: '{fileUuid ?? "NULL"}'");
                
                // Log de toutes les propriétés des métadonnées pour diagnostic
                Info($"Toutes les propriétés des métadonnées:");
                foreach (var metaProp in metadataElement.EnumerateObject())
                {
                    var value = metaProp.Value.ValueKind == JsonValueKind.String 
                        ? metaProp.Value.GetString() 
                        : metaProp.Value.ToString();
                    Info($"  {metaProp.Name}: '{value}' (type: {metaProp.Value.ValueKind})");
                }
                
                // Validation des métadonnées essentielles
                if (string.IsNullOrEmpty(patientNumber) || string.IsNullOrEmpty(fileUuid))
                {
                    Error("ERREUR: Métadonnées incomplètes");
                    Error($"patient_number manquant: {string.IsNullOrEmpty(patientNumber)}");
                    Error($"uuid manquant: {string.IsNullOrEmpty(fileUuid)}");
                    throw new InvalidOperationException("Métadonnées incomplètes (patient_number ou uuid manquant)");
                }
                
                Info($"=== DÉBUT DU TRAITEMENT ===");
                Info($"Patient: {patientNumber}");
                Info($"UUID: {fileUuid}");

                // Création de la structure de répertoires pour organiser les fichiers par patient
                var jsonDir = Path.Combine(MountedLabPath, patientNumber, "logs", "json");
                var hl7Dir = Path.Combine(MountedLabPath, patientNumber, "hl7");
                
                // Création des répertoires s'ils n'existent pas déjà
                Directory.CreateDirectory(jsonDir);
                Directory.CreateDirectory(hl7Dir);
                
                // Sauvegarde des données JSON originales reçues de l'API
                var jsonFilename = $"result_{fileUuid}.json";
                var jsonFilePath = Path.Combine(jsonDir, jsonFilename);
                
                File.WriteAllText(jsonFilePath, content, Encoding.UTF8);
                Info($"Fichier JSON des données brutes sauvegardé: {jsonFilePath}");
                
                // Génération du fichier HL7 à partir des données JSON
                var hl7Content = GenerateHl7Content(data, patientNumber, fileUuid);
                var hl7Filename = $"result_{fileUuid}.hl7";
                var hl7FilePath = Path.Combine(hl7Dir, hl7Filename);
                
                // Sauvegarde avec encodage Windows-1252
                var encoding = GetWindows1252Encoding();
                File.WriteAllText(hl7FilePath, hl7Content, encoding);
                Info($"Fichier HL7 sauvegardé dans le répertoire patient: {hl7FilePath}");
                
                // Copie supplémentaire du fichier HL7 à la racine pour compatibilité
                var hl7RootPath = Path.Combine(MountedLabPath, hl7Filename);
                File.WriteAllText(hl7RootPath, hl7Content, encoding);
                Info($"Fichier HL7 également copié à la racine: {hl7RootPath}");
                
                // Recherche et traitement du fichier PDF correspondant
                var submittedDir = Path.Combine(MountedLabPath, "SUBMITTED");
                var pdfFilename = FindFileByUuid(submittedDir, fileUuid);
                
                if (!string.IsNullOrEmpty(pdfFilename))
                {
                    var pdfFile = Path.Combine(submittedDir, pdfFilename);
                    if (File.Exists(pdfFile))
                    {
                        var patientDir = Path.Combine(MountedLabPath, patientNumber);
                        Directory.CreateDirectory(patientDir);
                        
                        var pdfDest = Path.Combine(patientDir, pdfFilename);
                        File.Move(pdfFile, pdfDest);
                        Info($"PDF déplacé avec succès vers: {pdfDest}");
                    }
                    else
                    {
                        Error($"PDF physiquement absent dans SUBMITTED: {pdfFile}");
                    }
                }
                else
                {
                    Error($"Aucun fichier PDF trouvé avec l'UUID: {fileUuid}");
                }

                return new DownloadResult
                {
                    Status = "success",
                    PatientNumber = patientNumber,
                    FileUuid = fileUuid,
                    Message = "Téléchargement et conversion réussis"
                };
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du téléchargement et de la sauvegarde: {0}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Obtient l'encodage Windows-1252 de manière sécurisée
        /// </summary>
        /// <returns>L'encodage Windows-1252 ou UTF-8 en cas d'échec</returns>
        private Encoding GetWindows1252Encoding()
        {
            try
            {
                return Encoding.GetEncoding("windows-1252");
            }
            catch (Exception ex)
            {
                ErrorFormat("Impossible d'obtenir l'encodage Windows-1252, utilisation d'UTF-8 à la place: {0}", ex.Message);
                return Encoding.UTF8;
            }
        }

        private string? FindFileByUuid(string submittedDir, string jobUuid)
        {
            if (!Directory.Exists(submittedDir))
            {
                Error($"Répertoire SUBMITTED non trouvé: {submittedDir}");
                return null;
            }

            try
            {
                var files = Directory.GetFiles(submittedDir);
                foreach (var file in files)
                {
                    var filename = Path.GetFileName(file);
                    if (filename.Contains(jobUuid))
                    {
                        return filename;
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la recherche du fichier PDF: {0}", ex.Message);
            }

            return null;
        }

        private string GenerateHl7Content(JsonDocument jsonData, string patientNumber, string fileUuid)
        {
            try
            {
                // Timestamp actuel au format HL7
                var currentTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                
                // En-tête du message HL7 (MSH)
                var msh = $"MSH|^~\\&|THEMA|EMA_LAB|EMA_LAB|HOSPITAL|{currentTime}||ORU^R01|{fileUuid}|P|2.5.1\r";
                
                // Identification du patient (PID) - format simplifié
                var pid = $"PID|1||{patientNumber}||DOE^JOHN^^^^||19700101|U\r";
                
                // Récupération des données de laboratoire
                var obrObxSegments = new StringBuilder();
                
                if (jsonData.RootElement.TryGetProperty("data", out var dataElement) && dataElement.ValueKind == JsonValueKind.Array)
                {
                    var exams = new Dictionary<string, List<JsonElement>>();
                    
                    // Grouper les résultats par nom d'examen
                    foreach (var result in dataElement.EnumerateArray())
                    {
                        if (result.TryGetProperty("nom_de_l_examen", out var examNameElement))
                        {
                            var examName = examNameElement.GetString() ?? "UNKNOWN";
                            if (!exams.ContainsKey(examName))
                            {
                                exams[examName] = new List<JsonElement>();
                            }
                            exams[examName].Add(result);
                        }
                    }
                    
                    // Générer les segments OBR et OBX pour chaque examen
                    int obrId = 1;
                    int obxId = 1;
                    
                    foreach (var exam in exams)
                    {
                        var examName = exam.Key;
                        var results = exam.Value;
                        
                        // Segment OBR
                        var obr = $"OBR|{obrId}||{obrId}|{examName}||{currentTime}|{currentTime}|||||||{currentTime}||||||||||F\r";
                        obrObxSegments.Append(obr);
                        
                        // Segments OBX pour chaque résultat dans cet examen
                        foreach (var result in results)
                        {
                            var variableName = result.TryGetProperty("nom_de_la_variable", out var varElement) 
                                ? varElement.GetString() ?? "UNKNOWN" : "UNKNOWN";
                            var value = result.TryGetProperty("valeur_du_patient", out var valElement) 
                                ? valElement.GetString() ?? "" : "";
                            var unit = result.TryGetProperty("unité", out var unitElement) 
                                ? unitElement.GetString() ?? "" : "";
                            var refLow = result.TryGetProperty("valeur_minimale", out var minElement) 
                                ? minElement.GetString() ?? "" : "";
                            var refHigh = result.TryGetProperty("valeur_maximale", out var maxElement) 
                                ? maxElement.GetString() ?? "" : "";
                            
                            // Déterminer si le résultat est anormal
                            var abnormalFlag = "";
                            if (double.TryParse(value, out var valueFloat) && 
                                double.TryParse(refLow, out var minFloat) && 
                                double.TryParse(refHigh, out var maxFloat))
                            {
                                if (valueFloat < minFloat)
                                    abnormalFlag = "L";  // Low
                                else if (valueFloat > maxFloat)
                                    abnormalFlag = "H";  // High
                            }
                            
                            // Segment OBX
                            var obx = $"OBX|{obxId}|NM|{variableName}||{value}|{unit}|{refLow}-{refHigh}|{abnormalFlag}|||F\r";
                            obrObxSegments.Append(obx);
                            obxId++;
                        }
                        
                        obrId++;
                    }
                }
                
                // Combiner tous les segments
                var hl7Message = msh + pid + obrObxSegments.ToString();
                
                return hl7Message;
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la génération du contenu HL7: {0}", ex.Message);
                throw;
            }
        }
    }

    /// <summary>
    /// Classe représentant le résultat d'un téléchargement
    /// </summary>
    public class DownloadResult
    {
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? PatientNumber { get; set; }
        public string? FileUuid { get; set; }
        public string? PdfPath { get; set; }
        public string? Hl7Path { get; set; }
    }
}
