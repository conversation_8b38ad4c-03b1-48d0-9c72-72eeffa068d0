

using System.Threading;
using System.Xml.Linq;
using System.Text.Json;
using Wexflow.Core;


// ========================================
// CustomFileWatcher - Surveillance de fichiers avec filtrage par date
// ========================================
namespace Wexflow.Tasks.BiodataFileWatcher
{
    /// <summary>
    /// Tâche personnalisée qui hérite de FileWatcher mais ne déclenche que les fichiers 
    /// nouvellement créés après la date d'activation du workflow
    /// </summary>
    public class BiodataFileWatcher : Core.Task
    {
        public string WatchPath { get; }
        public string FilePattern { get; }
        public bool IncludeSubfolders { get; }
        public DateTime WorkflowStartTime { get; private set; }
        public string StateFilePath { get; }
        public int OnFileCreated { get; }

        private FileSystemWatcher? _fileWatcher;
        private readonly object _lockObject = new object();

        public BiodataFileWatcher(XElement xe, Workflow wf) : base(xe, wf)
        {
            // Configuration depuis le XML
            WatchPath = GetSetting("watchPath", "/opt/airflow/data") ?? "/opt/airflow/data";
            FilePattern = GetSetting("filePattern", "*") ?? "*";
            IncludeSubfolders = bool.Parse(GetSetting("includeSubfolders", "true") ?? "true");
            OnFileCreated = int.Parse(GetSetting("onFileCreated", "-1") ?? "-1");
            
            // Fichier d'état pour persister la date de démarrage
            StateFilePath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "Wexflow",
                "BiodataFileWatcher",
                $"state_{Id}.json"
            );

            // Initialiser ou récupérer la date de démarrage du workflow
            InitializeWorkflowStartTime();
        }

        public override Core.TaskStatus Run()
        {
            Info("Démarrage de BiodataFileWatcher...");
            
            try
            {
                // Construire le chemin complet avec le pattern
                var fullWatchPath = BuildWatchPath();

                Info($"Fullwatchpath  :{fullWatchPath}");

                // Pour les chemins racines comme "J:\", Path.GetDirectoryName retourne null
                var directoryToCheck = Path.GetDirectoryName(fullWatchPath) ?? fullWatchPath;
                
                if (!Directory.Exists(directoryToCheck))
                {
                    Error($"Surveillance du répertoire fullWatchPath : {fullWatchPath}");
                    Error($"Le répertoire de surveillance n'existe pas directoryToCheck: {directoryToCheck}");
                    return new Core.TaskStatus(Status.Error, false);
                }
                Info($"Surveillance du répertoire: {directoryToCheck}");
                Info($"Surveillance du répertoire: {fullWatchPath}");
                Info($"Pattern de fichiers: {FilePattern}");
                Info($"Date de démarrage du workflow: {WorkflowStartTime:yyyy-MM-dd HH:mm:ss}");
                Info($"Inclure les sous-dossiers: {IncludeSubfolders}");

                // Vérifier les fichiers existants qui ont été créés après le démarrage
                CheckExistingFiles(fullWatchPath);

                // Démarrer la surveillance en temps réel
                StartFileWatcher(fullWatchPath);

                Info("BiodataFileWatcher démarré avec succès");
                return new Core.TaskStatus(Status.Success, false);
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur dans BiodataFileWatcher: {0}", ex.Message);
                return new Core.TaskStatus(Status.Error, false);
            }
        }

        private string BuildWatchPath()
        {
            // Construire le chemin selon le pattern fourni
            // filepath=os.path.join('/opt/airflow/data', '*', 'bbio', '*', '{' + file_pattern_str + '}')
            
            var basePath = WatchPath; // /opt/airflow/data ou J:\
            
          
            
            // Pour la surveillance, on surveille le répertoire de base
            // et on filtrera les fichiers selon le pattern dans les sous-répertoires
            return basePath;
        }

        private void InitializeWorkflowStartTime()
        {
            try
            {
                // Créer le répertoire d'état s'il n'existe pas
                var stateDir = Path.GetDirectoryName(StateFilePath);
                if (!string.IsNullOrEmpty(stateDir) && !Directory.Exists(stateDir))
                {
                    Directory.CreateDirectory(stateDir);
                }

                // Vérifier si un fichier d'état existe déjà
                if (File.Exists(StateFilePath))
                {
                    var stateJson = File.ReadAllText(StateFilePath);
                    var state = JsonSerializer.Deserialize<WorkflowState>(stateJson);
                    if (state != null)
                    {
                        WorkflowStartTime = state.StartTime;
                        Info($"Date de démarrage récupérée depuis l'état: {WorkflowStartTime:yyyy-MM-dd HH:mm:ss}");
                    }
                    else
                    {
                        WorkflowStartTime = DateTime.Now;
                        SaveWorkflowState();
                        Info($"État invalide - nouvelle date de démarrage enregistrée: {WorkflowStartTime:yyyy-MM-dd HH:mm:ss}");
                    }
                }
                else
                {
                    // Premier démarrage - enregistrer la date actuelle
                    WorkflowStartTime = DateTime.Now;
                    SaveWorkflowState();
                    Info($"Premier démarrage - date de démarrage enregistrée: {WorkflowStartTime:yyyy-MM-dd HH:mm:ss}");
                }
            }
            catch (Exception ex)
            {
                // En cas d'erreur, utiliser la date actuelle
                WorkflowStartTime = DateTime.Now;
                ErrorFormat("Erreur lors de l'initialisation de la date de démarrage: {0}", ex.Message);
            }
        }

        private void SaveWorkflowState()
        {
            try
            {
                var state = new WorkflowState
                {
                    StartTime = WorkflowStartTime,
                    LastUpdate = DateTime.Now
                };

                var stateJson = JsonSerializer.Serialize(state, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(StateFilePath, stateJson);
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la sauvegarde de l'état: {0}", ex.Message);
            }
        }

        private void CheckExistingFiles(string watchPath)
        {
            try
            {
                Info("Vérification des fichiers existants...");

                // Vérifier que le répertoire de surveillance existe
                if (!Directory.Exists(watchPath))
                {
                    Error($"Impossible de vérifier les fichiers existants: le répertoire {watchPath} n'existe pas");
                    return;
                }

                // Rechercher tous les fichiers PDF dans le répertoire de surveillance et ses sous-répertoires
                var allPdfFiles = Directory.GetFiles(watchPath, "*.pdf", SearchOption.AllDirectories);
                
                Info($"{allPdfFiles.Length} fichiers PDF trouvés au total dans {watchPath} et ses sous-répertoires");
                
                int foundFiles = 0;

                foreach (var file in allPdfFiles)
                {
                    // Vérifier si le fichier correspond au pattern souhaité
                    if (IsFileMatchingPattern(file))
                    {
                        var fileInfo = new FileInfo(file);
                        
                        // Vérifier si le fichier a été créé après le démarrage du workflow
                        if (fileInfo.CreationTime > WorkflowStartTime)
                        {
                            Info($"Fichier existant détecté (créé après démarrage): {file}");
                            ProcessNewFile(file);
                            foundFiles++;
                        }
                        else
                        {
                            InfoFormat("Fichier ignoré (créé avant démarrage): {0} (créé le {1:yyyy-MM-dd HH:mm:ss})", 
                                file, fileInfo.CreationTime);
                        }
                    }
                }

                Info($"Vérification terminée. {foundFiles} fichiers existants traités.");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la vérification des fichiers existants: {0}", ex.Message);
            }
        }

        private void StartFileWatcher(string watchPath)
        {
            try
            {
                // Vérifier que le répertoire existe
                if (!Directory.Exists(watchPath))
                {
                    // Afficher un message d'erreur plus détaillé
                    Error($"Impossible de démarrer la surveillance: le répertoire {watchPath} n'existe pas et ne peut pas être créé automatiquement.");
                    throw new DirectoryNotFoundException($"Le répertoire de surveillance n'existe pas: {watchPath}");
                }
                
                Info($"Démarrage de la surveillance sur le répertoire existant: {watchPath}");

                _fileWatcher = new FileSystemWatcher(watchPath)
                {
                    IncludeSubdirectories = true, // Toujours inclure les sous-répertoires pour ce cas d'usage
                    NotifyFilter = NotifyFilters.CreationTime | NotifyFilters.FileName,
                    EnableRaisingEvents = true
                };

                _fileWatcher.Created += OnFileCreatedEvent;
                _fileWatcher.Error += OnFileWatcherError;

                Info("FileSystemWatcher démarré");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du démarrage de FileSystemWatcher: {0}", ex.Message);
                throw;
            }
        }

        private void OnFileCreatedEvent(object sender, FileSystemEventArgs e)
        {
            try
            {
                lock (_lockObject)
                {
                    // Vérifier si le fichier correspond au pattern spécifique
                    if (IsFileMatchingPattern(e.FullPath))
                    {
                        var fileInfo = new FileInfo(e.FullPath);
                        
                        // Double vérification : le fichier doit être créé après le démarrage du workflow
                        if (fileInfo.CreationTime > WorkflowStartTime)
                        {
                            Info($"Nouveau fichier détecté: {e.FullPath}");
                            ProcessNewFile(e.FullPath);
                        }
                        else
                        {
                            InfoFormat("Fichier ignoré (créé avant démarrage): {0} (créé le {1:yyyy-MM-dd HH:mm:ss})", 
                                e.FullPath, fileInfo.CreationTime);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du traitement de l'événement de création de fichier: {0}", ex.Message);
            }
        }

        private bool IsFileMatchingPattern(string filePath)
        {
            try
            {
                // Vérifier si le chemin correspond au pattern:
                // watchpath_setting/*/bbio/*/[filename.pdf]
                
                var relativePath = Path.GetRelativePath(WatchPath, filePath);
                var pathParts = relativePath.Split(Path.DirectorySeparatorChar);

                // Le chemin doit avoir au moins 4 parties: [dir1]/bbio/[dir2]/[filename]
                // Mais peut aussi avoir plus de niveaux: [dir1]/bbio/[dir2]/[dir3]/[filename]
                if (pathParts.Length < 4)
                    return false;

                // Vérifier si "bbio" est présent dans le chemin
                bool hasBbioDir = false;
                int bbioIndex = -1;
                
                for (int i = 0; i < pathParts.Length; i++)
                {
                    if (pathParts[i] == "bbio")
                    {
                        hasBbioDir = true;
                        bbioIndex = i;
                        break;
                    }
                }
                
                if (!hasBbioDir)
                    return false;
                
                // Vérifier qu'il y a au moins un niveau après "bbio"
                if (bbioIndex >= pathParts.Length - 2)
                    return false;

                // Vérifier que le fichier est un PDF
                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                if (extension != ".pdf")
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la vérification du pattern: {0}", ex.Message);
                return false;
            }
        }

        private bool MatchesPattern(string fileName, string pattern)
        {
            // Implémentation simple de correspondance de pattern avec wildcards
            if (pattern == "*" || pattern == "*.*")
                return true;

            if (pattern.Contains("*"))
            {
                // Convertir le pattern en regex simple
                var regexPattern = pattern.Replace("*", ".*").Replace("?", ".");
                return System.Text.RegularExpressions.Regex.IsMatch(fileName, regexPattern, 
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            }

            return string.Equals(fileName, pattern, StringComparison.OrdinalIgnoreCase);
        }

        private void ProcessNewFile(string filePath)
        {
            try
            {
                Info($"Traitement du nouveau fichier: {filePath}");

                // Créer un FileInf pour Wexflow
                var fileInf = new FileInf(filePath, Id);
                Files.Add(fileInf);

                // Si une tâche suivante est configurée, la déclencher
                if (OnFileCreated > 0)
                {
                    Info($"Déclenchement de la tâche suivante (ID: {OnFileCreated})");
                    
                    // Dans Wexflow, vous pouvez déclencher une tâche suivante
                    // Cette partie dépend de l'implémentation spécifique de votre version de Wexflow
                    TriggerNextTask(OnFileCreated, filePath);
                }

                Info($"Fichier traité avec succès: {filePath}");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du traitement du fichier {0}: {1}", filePath, ex.Message);
            }
        }

        private void TriggerNextTask(int taskId, string filePath)
        {
            try
            {
                // Cette méthode dépend de l'implémentation de Wexflow
                // Vous devrez adapter selon votre version
                
                Info($"Déclenchement de la tâche {taskId} pour le fichier {filePath}");
                
                // Exemple d'implémentation - à adapter selon votre version de Wexflow
                // Workflow.StartTask(taskId);
                
                // Ou utiliser le système d'événements de Wexflow si disponible
                // Workflow.TriggerTask(taskId, new Dictionary<string, object> { { "filePath", filePath } });
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du déclenchement de la tâche {0}: {1}", taskId, ex.Message);
            }
        }

        private void OnFileWatcherError(object sender, ErrorEventArgs e)
        {
            ErrorFormat("Erreur FileSystemWatcher: {0}", e.GetException().Message);
            
            // Tentative de redémarrage du watcher
            try
            {
                _fileWatcher?.Dispose();
                StartFileWatcher(BuildWatchPath());
                Info("FileSystemWatcher redémarré après erreur");
            }
            catch (Exception ex)
            {
                ErrorFormat("Impossible de redémarrer FileSystemWatcher: {0}", ex.Message);
            }
        }

        public void StopFileWatcher()
        {
            try
            {
                _fileWatcher?.Dispose();
                Info("BiodataFileWatcher arrêté");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de l'arrêt de BiodataFileWatcher: {0}", ex.Message);
            }
        }

        /// <summary>
        /// Réinitialise la date de démarrage du workflow (utile pour les tests ou redémarrages)
        /// </summary>
        public void ResetWorkflowStartTime()
        {
            try
            {
                WorkflowStartTime = DateTime.Now;
                SaveWorkflowState();
                Info($"Date de démarrage réinitialisée: {WorkflowStartTime:yyyy-MM-dd HH:mm:ss}");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la réinitialisation: {0}", ex.Message);
            }
        }
    }

// ========================================
// ConfigurationHelper - Utilitaire pour lire les fichiers de configuration
// ========================================
    public static class ConfigurationHelper
{
    private static Dictionary<string, string> _variables = new Dictionary<string, string>();

    static ConfigurationHelper()
    {
        LoadConfiguration();
    }

    private static void LoadConfiguration()
    {
        try
        {
            var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config", "variables.json");
            if (File.Exists(configPath))
            {
                var jsonContent = File.ReadAllText(configPath);
                var jsonDoc = JsonSerializer.Deserialize<Dictionary<string, string>>(jsonContent);
                _variables = jsonDoc ?? new Dictionary<string, string>();
            }
            else
            {
                _variables = new Dictionary<string, string>();
            }
        }
        catch (Exception)
        {
            _variables = new Dictionary<string, string>();
        }
    }

    public static string? GetSetting(string key, string? defaultValue = null)
    {
        if (_variables.TryGetValue(key, out var value))
        {
            return value;
        }
        return defaultValue;
    }
}


    /// <summary>
    /// Classe pour persister l'état du workflow
    /// </summary>
    public class WorkflowState
    {
        public DateTime StartTime { get; set; }
        public DateTime LastUpdate { get; set; }
    }
}
