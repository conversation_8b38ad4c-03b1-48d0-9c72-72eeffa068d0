

using System.Threading;
using System.Xml.Linq;
using System.Text.Json;
using System.Net.Http;
using Wexflow.Core;


// ========================================
// CustomFileWatcher - Surveillance de fichiers avec filtrage par date
// ========================================
namespace Wexflow.Tasks.BiodataFileWatcher
{
    /// <summary>
    /// Tâche personnalisée qui hérite de FileWatcher mais ne déclenche que les fichiers 
    /// nouvellement créés après la date d'activation du workflow
    /// </summary>
    public class BiodataFileWatcher : Core.Task
    {
        public string WatchPath { get; }
        public string FilePattern { get; }
        public bool IncludeSubfolders { get; }
        public DateTime WorkflowStartTime { get; private set; }
        public string StateFilePath { get; }
        public int OnFileCreated { get; }

        private FileSystemWatcher? _fileWatcher;
        private readonly object _lockObject = new object();

        public BiodataFileWatcher(XElement xe, Workflow wf) : base(xe, wf)
        {
            // Configuration depuis le XML
            WatchPath = GetSetting("watchPath", "/opt/airflow/data") ?? "/opt/airflow/data";
            FilePattern = GetSetting("filePattern", "*") ?? "*";
            IncludeSubfolders = bool.Parse(GetSetting("includeSubfolders", "true") ?? "true");
            OnFileCreated = int.Parse(GetSetting("onFileCreated", "-1") ?? "-1");
            
            // Fichier d'état pour persister la date de démarrage
            StateFilePath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "Wexflow",
                "BiodataFileWatcher",
                $"state_{Id}.json"
            );

            // Initialiser ou récupérer la date de démarrage du workflow
            InitializeWorkflowStartTime();
        }

        public override Core.TaskStatus Run()
        {
            Info("Démarrage de BiodataFileWatcher...");
            
            try
            {
                // Construire le chemin complet avec le pattern
                var fullWatchPath = BuildWatchPath();

                Info($"Fullwatchpath  :{fullWatchPath}");

                // Pour les chemins racines comme "J:\", Path.GetDirectoryName retourne null
                var directoryToCheck = Path.GetDirectoryName(fullWatchPath) ?? fullWatchPath;
                
                if (!Directory.Exists(directoryToCheck))
                {
                    Error($"Surveillance du répertoire fullWatchPath : {fullWatchPath}");
                    Error($"Le répertoire de surveillance n'existe pas directoryToCheck: {directoryToCheck}");
                    return new Core.TaskStatus(Status.Error, false);
                }
                Info($"Surveillance du répertoire: {directoryToCheck}");
                Info($"Surveillance du répertoire: {fullWatchPath}");
                Info($"Pattern de fichiers: {FilePattern}");
                Info($"Date de démarrage du workflow: {WorkflowStartTime:yyyy-MM-dd HH:mm:ss}");
                Info($"Inclure les sous-dossiers: {IncludeSubfolders}");

                // Vérifier les fichiers existants qui ont été créés après le démarrage
                CheckExistingFiles(fullWatchPath);

                // Démarrer la surveillance en temps réel
                StartFileWatcher(fullWatchPath);

                Info("BiodataFileWatcher démarré avec succès");
                return new Core.TaskStatus(Status.Success, false);
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur dans BiodataFileWatcher: {0}", ex.Message);
                return new Core.TaskStatus(Status.Error, false);
            }
        }

        private string BuildWatchPath()
        {
            // Construire le chemin selon le pattern fourni
            // filepath=os.path.join('/opt/airflow/data', '*', 'bbio', '*', '{' + file_pattern_str + '}')
            
            var basePath = WatchPath; // /opt/airflow/data ou J:\
            
          
            
            // Pour la surveillance, on surveille le répertoire de base
            // et on filtrera les fichiers selon le pattern dans les sous-répertoires
            return basePath;
        }

        private void InitializeWorkflowStartTime()
        {
            try
            {
                // Créer le répertoire d'état s'il n'existe pas
                var stateDir = Path.GetDirectoryName(StateFilePath);
                if (!string.IsNullOrEmpty(stateDir) && !Directory.Exists(stateDir))
                {
                    Directory.CreateDirectory(stateDir);
                }

                // Vérifier si un fichier d'état existe déjà
                if (File.Exists(StateFilePath))
                {
                    var stateJson = File.ReadAllText(StateFilePath);
                    var state = JsonSerializer.Deserialize<WorkflowState>(stateJson);
                    if (state != null)
                    {
                        WorkflowStartTime = state.StartTime;
                        Info($"Date de démarrage récupérée depuis l'état: {WorkflowStartTime:yyyy-MM-dd HH:mm:ss}");
                    }
                    else
                    {
                        WorkflowStartTime = DateTime.Now;
                        SaveWorkflowState();
                        Info($"État invalide - nouvelle date de démarrage enregistrée: {WorkflowStartTime:yyyy-MM-dd HH:mm:ss}");
                    }
                }
                else
                {
                    // Premier démarrage - enregistrer la date actuelle
                    WorkflowStartTime = DateTime.Now;
                    SaveWorkflowState();
                    Info($"Premier démarrage - date de démarrage enregistrée: {WorkflowStartTime:yyyy-MM-dd HH:mm:ss}");
                }
            }
            catch (Exception ex)
            {
                // En cas d'erreur, utiliser la date actuelle
                WorkflowStartTime = DateTime.Now;
                ErrorFormat("Erreur lors de l'initialisation de la date de démarrage: {0}", ex.Message);
            }
        }

        private void SaveWorkflowState()
        {
            try
            {
                var state = new WorkflowState
                {
                    StartTime = WorkflowStartTime,
                    LastUpdate = DateTime.Now
                };

                var stateJson = JsonSerializer.Serialize(state, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(StateFilePath, stateJson);
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la sauvegarde de l'état: {0}", ex.Message);
            }
        }

        private void CheckExistingFiles(string watchPath)
        {
            try
            {
                Info("Vérification des fichiers existants...");

                // Vérifier que le répertoire de surveillance existe
                if (!Directory.Exists(watchPath))
                {
                    Error($"Impossible de vérifier les fichiers existants: le répertoire {watchPath} n'existe pas");
                    return;
                }

                // Rechercher tous les fichiers PDF dans le répertoire de surveillance et ses sous-répertoires
                var allPdfFiles = Directory.GetFiles(watchPath, "*.pdf", SearchOption.AllDirectories);
                
                Info($"{allPdfFiles.Length} fichiers PDF trouvés au total dans {watchPath} et ses sous-répertoires");
                
                int foundFiles = 0;

                foreach (var file in allPdfFiles)
                {
                    // Vérifier si le fichier correspond au pattern souhaité
                    if (IsFileMatchingPattern(file))
                    {
                        var fileInfo = new FileInfo(file);
                        
                        // Vérifier si le fichier a été créé après le démarrage du workflow
                        if (fileInfo.CreationTime > WorkflowStartTime)
                        {
                            Info($"Fichier existant détecté (créé après démarrage): {file}");
                            ProcessNewFile(file);
                            foundFiles++;
                        }
                        else
                        {
                            InfoFormat("Fichier ignoré (créé avant démarrage): {0} (créé le {1:yyyy-MM-dd HH:mm:ss})", 
                                file, fileInfo.CreationTime);
                        }
                    }
                }

                Info($"Vérification terminée. {foundFiles} fichiers existants traités.");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la vérification des fichiers existants: {0}", ex.Message);
            }
        }

        private void StartFileWatcher(string watchPath)
        {
            try
            {
                // Vérifier que le répertoire existe
                if (!Directory.Exists(watchPath))
                {
                    // Afficher un message d'erreur plus détaillé
                    Error($"Impossible de démarrer la surveillance: le répertoire {watchPath} n'existe pas et ne peut pas être créé automatiquement.");
                    throw new DirectoryNotFoundException($"Le répertoire de surveillance n'existe pas: {watchPath}");
                }
                
                Info($"Démarrage de la surveillance sur le répertoire existant: {watchPath}");

                _fileWatcher = new FileSystemWatcher(watchPath)
                {
                    IncludeSubdirectories = true, // Toujours inclure les sous-répertoires pour ce cas d'usage
                    NotifyFilter = NotifyFilters.CreationTime | NotifyFilters.FileName,
                    EnableRaisingEvents = true
                };

                _fileWatcher.Created += OnFileCreatedEvent;
                _fileWatcher.Error += OnFileWatcherError;

                Info("FileSystemWatcher démarré");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du démarrage de FileSystemWatcher: {0}", ex.Message);
                throw;
            }
        }

        private void OnFileCreatedEvent(object sender, FileSystemEventArgs e)
        {
            try
            {
                lock (_lockObject)
                {
                    // Vérifier si le fichier correspond au pattern spécifique
                    if (IsFileMatchingPattern(e.FullPath))
                    {
                        var fileInfo = new FileInfo(e.FullPath);
                        
                        // Double vérification : le fichier doit être créé après le démarrage du workflow
                        if (fileInfo.CreationTime > WorkflowStartTime)
                        {
                            Info($"Nouveau fichier détecté: {e.FullPath}");
                            ProcessNewFile(e.FullPath);
                        }
                        else
                        {
                            InfoFormat("Fichier ignoré (créé avant démarrage): {0} (créé le {1:yyyy-MM-dd HH:mm:ss})", 
                                e.FullPath, fileInfo.CreationTime);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du traitement de l'événement de création de fichier: {0}", ex.Message);
            }
        }

        private bool IsFileMatchingPattern(string filePath)
        {
            try
            {
                // Vérifier si le chemin correspond au pattern:
                // watchpath_setting/*/bbio/*/[filename.pdf]
                
                var relativePath = Path.GetRelativePath(WatchPath, filePath);
                var pathParts = relativePath.Split(Path.DirectorySeparatorChar);

                // Le chemin doit avoir au moins 4 parties: [dir1]/bbio/[dir2]/[filename]
                // Mais peut aussi avoir plus de niveaux: [dir1]/bbio/[dir2]/[dir3]/[filename]
                if (pathParts.Length < 4)
                    return false;

                // Vérifier si "bbio" est présent dans le chemin
                bool hasBbioDir = false;
                int bbioIndex = -1;
                
                for (int i = 0; i < pathParts.Length; i++)
                {
                    if (pathParts[i] == "bbio")
                    {
                        hasBbioDir = true;
                        bbioIndex = i;
                        break;
                    }
                }
                
                if (!hasBbioDir)
                    return false;
                
                // Vérifier qu'il y a au moins un niveau après "bbio"
                if (bbioIndex >= pathParts.Length - 2)
                    return false;

                // Vérifier que le fichier est un PDF
                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                if (extension != ".pdf")
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la vérification du pattern: {0}", ex.Message);
                return false;
            }
        }

        private bool MatchesPattern(string fileName, string pattern)
        {
            // Implémentation simple de correspondance de pattern avec wildcards
            if (pattern == "*" || pattern == "*.*")
                return true;

            if (pattern.Contains("*"))
            {
                // Convertir le pattern en regex simple
                var regexPattern = pattern.Replace("*", ".*").Replace("?", ".");
                return System.Text.RegularExpressions.Regex.IsMatch(fileName, regexPattern, 
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            }

            return string.Equals(fileName, pattern, StringComparison.OrdinalIgnoreCase);
        }

        private void ProcessNewFile(string filePath)
        {
            try
            {
                Info($"Traitement du nouveau fichier: {filePath}");

                // Créer un FileInf pour Wexflow
                var fileInf = new FileInf(filePath, Id);
                Files.Add(fileInf);

                // Traitement du document avec la logique du DAG
                var processor = new BiodataDocumentProcessor();
                var processResult = processor.ProcessDetectedFile(filePath, this);

                // Si une tâche suivante est configurée, la déclencher
                if (OnFileCreated > 0)
                {
                    Info($"Déclenchement de la tâche suivante (ID: {OnFileCreated})");
                    TriggerNextTask(OnFileCreated, filePath);
                }

                Info($"Fichier traité avec succès: {filePath}");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du traitement du fichier {0}: {1}", filePath, ex.Message);
            }
        }

        private void TriggerNextTask(int taskId, string filePath)
        {
            try
            {
                // Cette méthode dépend de l'implémentation de Wexflow
                // Vous devrez adapter selon votre version
                
                Info($"Déclenchement de la tâche {taskId} pour le fichier {filePath}");
                
                // Exemple d'implémentation - à adapter selon votre version de Wexflow
                // Workflow.StartTask(taskId);
                
                // Ou utiliser le système d'événements de Wexflow si disponible
                // Workflow.TriggerTask(taskId, new Dictionary<string, object> { { "filePath", filePath } });
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du déclenchement de la tâche {0}: {1}", taskId, ex.Message);
            }
        }

        private void OnFileWatcherError(object sender, ErrorEventArgs e)
        {
            ErrorFormat("Erreur FileSystemWatcher: {0}", e.GetException().Message);
            
            // Tentative de redémarrage du watcher
            try
            {
                _fileWatcher?.Dispose();
                StartFileWatcher(BuildWatchPath());
                Info("FileSystemWatcher redémarré après erreur");
            }
            catch (Exception ex)
            {
                ErrorFormat("Impossible de redémarrer FileSystemWatcher: {0}", ex.Message);
            }
        }

        public void StopFileWatcher()
        {
            try
            {
                _fileWatcher?.Dispose();
                Info("BiodataFileWatcher arrêté");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de l'arrêt de BiodataFileWatcher: {0}", ex.Message);
            }
        }

        /// <summary>
        /// Réinitialise la date de démarrage du workflow (utile pour les tests ou redémarrages)
        /// </summary>
        public void ResetWorkflowStartTime()
        {
            try
            {
                WorkflowStartTime = DateTime.Now;
                SaveWorkflowState();
                Info($"Date de démarrage réinitialisée: {WorkflowStartTime:yyyy-MM-dd HH:mm:ss}");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la réinitialisation: {0}", ex.Message);
            }
        }
    }

// ========================================
// ConfigurationHelper - Utilitaire pour lire les fichiers de configuration
// ========================================
    public static class ConfigurationHelper
{
    private static Dictionary<string, string> _variables = new Dictionary<string, string>();

    static ConfigurationHelper()
    {
        LoadConfiguration();
    }

    private static void LoadConfiguration()
    {
        try
        {
            var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config", "variables.json");
            if (File.Exists(configPath))
            {
                var jsonContent = File.ReadAllText(configPath);
                var jsonDoc = JsonSerializer.Deserialize<Dictionary<string, string>>(jsonContent);
                _variables = jsonDoc ?? new Dictionary<string, string>();
            }
            else
            {
                _variables = new Dictionary<string, string>();
            }
        }
        catch (Exception)
        {
            _variables = new Dictionary<string, string>();
        }
    }

    public static string? GetSetting(string key, string? defaultValue = null)
    {
        if (_variables.TryGetValue(key, out var value))
        {
            return value;
        }
        return defaultValue;
    }
}


    /// <summary>
    /// Classe pour persister l'état du workflow
    /// </summary>
    public class WorkflowState
    {
        public DateTime StartTime { get; set; }
        public DateTime LastUpdate { get; set; }
    }

    /// <summary>
    /// Classe pour traiter les documents détectés selon la logique du DAG
    /// </summary>
    public class BiodataDocumentProcessor
    {
        private static readonly HttpClient _httpClient = new HttpClient();

        public FileProcessingResult ProcessDetectedFile(string filePath, BiodataFileWatcher watcher)
        {
            try
            {
                watcher.Info($"Début du traitement du fichier détecté: {filePath}");

                // Extraire le numéro du patient depuis le chemin
                var patientFolder = ExtractPatientFolder(filePath, watcher.WatchPath);
                if (string.IsNullOrEmpty(patientFolder))
                {
                    throw new InvalidOperationException($"Impossible d'extraire le numéro du patient du chemin: {filePath}");
                }

                // Obtenir le nom du fichier original
                var originalFilename = Path.GetFileName(filePath);
                var fileBase = Path.GetFileNameWithoutExtension(originalFilename);
                var fileExt = Path.GetExtension(originalFilename);

                // Vérifier le statut avant de traiter le fichier
                var fileStatus = CheckFileStatus(originalFilename, watcher);
                if (fileStatus == "failed")
                {
                    watcher.Info($"Le fichier {originalFilename} a le statut 'failed', aucune action ne sera effectuée");
                    return new FileProcessingResult
                    {
                        OriginalFilename = originalFilename,
                        PatientNumber = patientFolder,
                        OriginalPath = filePath,
                        Status = "failed",
                        ProcessedDate = DateTime.Now
                    };
                }

                // Créer la structure de répertoires de destination
                var mountedLabPath = ConfigurationHelper.GetSetting("repert_labo", "/tmp/mounted_lab");
                EnsureDirectoryExists(mountedLabPath, watcher);

                var submittedDir = Path.Combine(mountedLabPath, "SUBMITTED");
                EnsureDirectoryExists(submittedDir, watcher);

                // Générer un nouveau nom de fichier avec timestamp
                var timestamp = DateTime.Now.ToString("ddHHmmss");
                var newFilename = $"{fileBase}_{timestamp}{fileExt}";
                var newPath = Path.Combine(submittedDir, newFilename);

                // Copier le fichier vers le répertoire SUBMITTED
                File.Copy(filePath, newPath, true);
                watcher.Info($"Fichier copié vers: {newPath}");

                // Créer les informations du fichier
                var fileInfo = new FileProcessingResult
                {
                    OriginalFilename = originalFilename,
                    Filename = newFilename,
                    PatientNumber = patientFolder,
                    OriginalPath = filePath,
                    NewPath = newPath,
                    Status = "submitted",
                    ProcessedDate = DateTime.Now
                };

                // Traiter le document via l'API
                var apiResult = ProcessDocument(fileInfo, watcher);
                if (apiResult != null && !string.IsNullOrEmpty(apiResult.Uuid))
                {
                    // Renommer le fichier avec l'UUID
                    var uuidFilename = $"{fileBase}_{apiResult.Uuid}{fileExt}";
                    var uuidPath = Path.Combine(submittedDir, uuidFilename);
                    
                    File.Move(newPath, uuidPath);
                    watcher.Info($"Fichier renommé avec UUID: {uuidFilename}");

                    fileInfo.PreviousPath = newPath;
                    fileInfo.NewPath = uuidPath;
                    fileInfo.Filename = uuidFilename;
                    fileInfo.Uuid = apiResult.Uuid;
                }

                // Créer le fichier JSON de suivi
                CreateTrackingFile(fileInfo, mountedLabPath, watcher);

                // Marquer le fichier original comme traité
                MarkOriginalFileAsProcessed(filePath, watcher);

                watcher.Info($"Fichier traité avec succès: {fileInfo.Filename}");
                return fileInfo;
            }
            catch (Exception ex)
            {
                watcher.ErrorFormat("Erreur lors du traitement du fichier {0}: {1}", filePath, ex.Message);
                throw;
            }
        }

        private string ExtractPatientFolder(string filePath, string basePath)
        {
            try
            {
                var relativePath = Path.GetRelativePath(basePath, filePath);
                var pathParts = relativePath.Split(Path.DirectorySeparatorChar);
                return pathParts.Length > 0 ? pathParts[0] : null;
            }
            catch
            {
                return null;
            }
        }

        private string CheckFileStatus(string filename, BiodataFileWatcher watcher)
        {
            try
            {
                var themaApiUrl = ConfigurationHelper.GetSetting("thema_api_url", "https://biodata.hemadialyse.com");
                var baseUrl = themaApiUrl.TrimEnd('/');
                var url = $"{baseUrl}/files/status/{filename}";

                watcher.Info($"Vérification du statut du fichier via URL: {url}");

                var response = _httpClient.GetAsync(url).Result;
                if (response.IsSuccessStatusCode)
                {
                    var content = response.Content.ReadAsStringAsync().Result;
                    var statusData = JsonSerializer.Deserialize<Dictionary<string, object>>(content);
                    var status = statusData?.GetValueOrDefault("status")?.ToString();
                    watcher.Info($"Statut du fichier {filename}: {status}");
                    return status;
                }
                else
                {
                    watcher.ErrorFormat("Erreur lors de la vérification du statut: {0}", response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                watcher.ErrorFormat("Erreur lors de la vérification du statut: {0}", ex.Message);
                return null;
            }
        }

        private ApiProcessResult ProcessDocument(FileProcessingResult fileInfo, BiodataFileWatcher watcher)
        {
            try
            {
                watcher.Info($"Traitement du document via API: {fileInfo.Filename}");

                var themaApiUrl = ConfigurationHelper.GetSetting("thema_api_url", "https://biodata.hemadialyse.com");
                var baseUrl = themaApiUrl.TrimEnd('/');
                var url = $"{baseUrl}/workflow/process-document";

                if (!File.Exists(fileInfo.NewPath))
                {
                    throw new FileNotFoundException($"Le fichier n'existe pas: {fileInfo.NewPath}");
                }

                using var form = new MultipartFormDataContent();
                using var fileContent = new ByteArrayContent(File.ReadAllBytes(fileInfo.NewPath));
                fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/pdf");
                
                form.Add(fileContent, "file", fileInfo.Filename);
                form.Add(new StringContent(fileInfo.PatientNumber), "patient_number");
                form.Add(new StringContent("false"), "wait_for_completion");

                watcher.Info($"Envoi de la requête à l'API: {url}");

                var response = _httpClient.PostAsync(url, form).Result;
                if (response.IsSuccessStatusCode)
                {
                    var content = response.Content.ReadAsStringAsync().Result;
                    watcher.Info($"Réponse de l'API: {content}");

                    var apiResponse = JsonSerializer.Deserialize<Dictionary<string, object>>(content);
                    var uuid = apiResponse?.GetValueOrDefault("uuid")?.ToString();

                    return new ApiProcessResult
                    {
                        Success = true,
                        Uuid = uuid,
                        Response = content
                    };
                }
                else
                {
                    throw new HttpRequestException($"Erreur API: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                watcher.ErrorFormat("Erreur lors du traitement du document: {0}", ex.Message);
                throw;
            }
        }

        private void EnsureDirectoryExists(string path, BiodataFileWatcher watcher)
        {
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
                watcher.Info($"Répertoire créé: {path}");
            }
        }

        private void CreateTrackingFile(FileProcessingResult fileInfo, string mountedLabPath, BiodataFileWatcher watcher)
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var infoFilePath = Path.Combine(mountedLabPath, $"file_info_{timestamp}.json");

                var json = JsonSerializer.Serialize(fileInfo, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(infoFilePath, json);

                watcher.Info($"Fichier de suivi créé: {infoFilePath}");
            }
            catch (Exception ex)
            {
                watcher.ErrorFormat("Erreur lors de la création du fichier de suivi: {0}", ex.Message);
            }
        }

        private void MarkOriginalFileAsProcessed(string originalPath, BiodataFileWatcher watcher)
        {
            try
            {
                if (!File.Exists(originalPath))
                {
                    watcher.Info($"Le fichier original {originalPath} n'existe plus ou a déjà été traité");
                    return;
                }

                // Essayer de supprimer le fichier original
                File.Delete(originalPath);
                watcher.Info($"Fichier original supprimé: {originalPath}");
            }
            catch (UnauthorizedAccessException)
            {
                // Si on ne peut pas supprimer, essayer de le déplacer vers un dossier .processed
                try
                {
                    var originalDir = Path.GetDirectoryName(originalPath);
                    var processedDir = Path.Combine(originalDir, ".processed");
                    
                    if (!Directory.Exists(processedDir))
                    {
                        Directory.CreateDirectory(processedDir);
                    }

                    var processedPath = Path.Combine(processedDir, Path.GetFileName(originalPath));
                    File.Move(originalPath, processedPath);
                    watcher.Info($"Fichier déplacé vers .processed: {processedPath}");
                }
                catch (Exception ex)
                {
                    // Dernière tentative: renommer avec suffixe .processed
                    try
                    {
                        var processedName = originalPath + ".processed";
                        File.Move(originalPath, processedName);
                        watcher.Info($"Fichier renommé avec suffixe .processed: {processedName}");
                    }
                    catch (Exception renameEx)
                    {
                        watcher.ErrorFormat("Impossible de marquer le fichier comme traité: {0}", renameEx.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                watcher.ErrorFormat("Erreur lors du marquage du fichier comme traité: {0}", ex.Message);
            }
        }
    }

    /// <summary>
    /// Résultat du traitement d'un fichier
    /// </summary>
    public class FileProcessingResult
    {
        public string OriginalFilename { get; set; } = string.Empty;
        public string Filename { get; set; } = string.Empty;
        public string PatientNumber { get; set; } = string.Empty;
        public string OriginalPath { get; set; } = string.Empty;
        public string NewPath { get; set; } = string.Empty;
        public string PreviousPath { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Uuid { get; set; } = string.Empty;
        public DateTime ProcessedDate { get; set; }
    }

    /// <summary>
    /// Résultat de l'appel API
    /// </summary>
    public class ApiProcessResult
    {
        public bool Success { get; set; }
        public string Uuid { get; set; } = string.Empty;
        public string Response { get; set; } = string.Empty;
    }
}
