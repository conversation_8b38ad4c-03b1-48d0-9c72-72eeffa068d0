

using System.Threading;
using System.Xml.Linq;
using Wexflow.Core;


// ========================================
// MyTask - Tâche de base
// ========================================
namespace Wexflow.Tasks.MyTask
{
    public class MyTask : Core.Task
    {
        public MyTask(XElement xe, Workflow wf) : base(xe, wf) { }

        public override Core.TaskStatus Run()
        {
            try
            {
                // Ta logique ici
                Info("Hello from MyTask!");
                return new Core.TaskStatus(Status.Success);
            }
            catch (ThreadAbortException)
            {
                throw;
            }
        }
    }
}



// ========================================
// CustomCheckJobStatus - Vérification du statut des jobs
// ========================================
namespace Wexflow.Tasks.CustomCheckJobStatus
{
    public class CustomCheckJobStatus : Task
    {
        public string ThemaApiUrl { get; }

        private static readonly HttpClient httpClient = new HttpClient();

        public CustomCheckJobStatus(XElement xe, Workflow wf) : base(xe, wf)
        {
            ThemaApiUrl = ConfigurationHelper.GetSetting("thema_api_url", "https://biodata.hemadialyse.com");
        }

        public override Core.TaskStatus Run()
        {
            Info("Début de la vérification du statut des jobs...");

            try
            {
                // Récupérer les jobs actifs depuis la tâche précédente
                var activeJobs = GetActiveJobsFromPreviousTask();
                
                if (activeJobs == null || activeJobs.Count == 0)
                {
                    Info("Aucun job actif à vérifier");
                    return new Core.TaskStatus(Status.Success, false);
                }

                Info($"Vérification du statut de {activeJobs.Count} jobs");

                foreach (var job in activeJobs)
                {
                    try
                    {
                        Info($"Vérification du job ID: {job.Id}, UUID: {job.JobUuid}, Status actuel: {job.Status}");
                        CheckJobStatus(job.JobUuid).Wait();
                    }
                    catch (Exception ex)
                    {
                        ErrorFormat("Erreur lors de la vérification du job {0}: {1}", job.JobUuid, ex.Message);
                    }
                }

                Info("Vérification du statut des jobs terminée");
                return new Core.TaskStatus(Status.Success, false);
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur dans CustomCheckJobStatus: {0}", ex.Message);
                return new Core.TaskStatus(Status.Error, false);
            }
        }

        private List<JobInfo> GetActiveJobsFromPreviousTask()
        {
            try
            {
                // Dans Wexflow, récupérer les données de la tâche précédente
                // Cette implémentation dépend de la façon dont vous stockez les données entre les tâches
                
                // Pour l'exemple, nous simulons la récupération des jobs actifs
                // Dans une implémentation réelle, vous devriez récupérer les données
                // de la tâche CustomGetActiveJobs précédente
                
                var activeJobs = new List<JobInfo>();
                
                // TODO: Implémenter la logique pour récupérer les jobs de la tâche précédente
                // Par exemple, en lisant un fichier JSON créé par la tâche précédente
                // ou en utilisant le système de partage de données de Wexflow
                
                Info("Récupération des jobs actifs depuis la tâche précédente");
                
                return activeJobs;
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la récupération des jobs actifs: {0}", ex.Message);
                return new List<JobInfo>();
            }
        }

        private async System.Threading.Tasks.Task CheckJobStatus(string uuid)
        {
            try
            {
                var url = $"{ThemaApiUrl.TrimEnd('/')}/jobs/check";
                Info($"Vérification du statut du job via URL: {url}");

                // Préparer les données du formulaire
                var formData = new List<KeyValuePair<string, string>>
                {
                    new KeyValuePair<string, string>("job_id", uuid)
                };

                var formContent = new FormUrlEncodedContent(formData);

                // Effectuer la requête POST
                var response = await httpClient.PostAsync(url, formContent);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Info("L'API est disponible et prête à recevoir des requêtes.");
                    Info($"Réponse de l'API pour le job {uuid}: {responseContent}");

                    // Traiter la réponse si nécessaire
                    await ProcessJobStatusResponse(uuid, responseContent);
                }
                else
                {
                    throw new Exception($"L'API a répondu avec le code {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la vérification du statut du job {0}: {1}", uuid, ex.Message);
                throw;
            }
        }

        private async System.Threading.Tasks.Task ProcessJobStatusResponse(string uuid, string responseContent)
        {
            try
            {
                // Traiter la réponse de l'API de vérification du statut
                // Cette méthode peut être utilisée pour mettre à jour le statut du job
                // dans votre base de données ou effectuer d'autres actions selon la réponse
                
                Info($"Traitement de la réponse pour le job {uuid}");
                
                // Essayer de parser la réponse JSON si applicable
                try
                {
                    var jsonResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
                    
                    if (jsonResponse.TryGetProperty("status", out var statusElement))
                    {
                        var newStatus = statusElement.GetString();
                        Info($"Nouveau statut pour le job {uuid}: {newStatus}");
                        
                        // TODO: Mettre à jour le statut dans votre base de données
                        await UpdateJobStatusInDatabase(uuid, newStatus);
                    }
                }
                catch (JsonException)
                {
                    // Si ce n'est pas du JSON, traiter comme du texte simple
                    Info($"Réponse non-JSON reçue pour le job {uuid}: {responseContent}");
                }
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du traitement de la réponse pour le job {0}: {1}", uuid, ex.Message);
            }
        }

        private async System.Threading.Tasks.Task UpdateJobStatusInDatabase(string uuid, string newStatus)
        {
            try
            {
                // TODO: Implémenter la logique de mise à jour du statut dans la base de données
                // Cette méthode devrait mettre à jour le statut du job dans votre base de données
                
                Info($"Mise à jour du statut du job {uuid} vers {newStatus} dans la base de données");
                
                // Exemple de logique de base de données
                // using var connection = new SqlConnection(connectionString);
                // var query = "UPDATE jobs SET status = @status, updated_at = @updatedAt WHERE job_uuid = @uuid";
                // await connection.ExecuteAsync(query, new { status = newStatus, updatedAt = DateTime.Now, uuid = uuid });
                
                Info($"Statut du job {uuid} mis à jour avec succès");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la mise à jour du statut du job {0}: {1}", uuid, ex.Message);
                throw;
            }
        }
    }

    public class JobInfo
    {
        public int Id { get; set; }
        public string JobUuid { get; set; }
        public string Status { get; set; }
    }
}


// ========================================
// CustomDownloadBiodata - Téléchargement des données biodata
// ========================================
namespace Wexflow.Tasks.CustomDownloadBiodata
{
    public class CustomDownloadBiodata : Core.Task
    {
        public string ThemaApiUrl { get; }
        public string HmdApiUrl { get; }
        public string JwtToken { get; }
        public string MountedLabPath { get; }

        private static readonly HttpClient httpClient = new HttpClient();

        public CustomDownloadBiodata(XElement xe, Workflow wf) : base(xe, wf)
        {
            ThemaApiUrl = ConfigurationHelper.GetSetting("thema_api_url", "https://biodata.hemadialyse.com");
            HmdApiUrl = ConfigurationHelper.GetSetting("hmd_api_url", "http://10.1.16.50:8083");
            JwtToken = ConfigurationHelper.GetSetting("hmd_jwt_token", "");
            MountedLabPath = ConfigurationHelper.GetSetting("repert_labo", "/mnt/remote_share_processed");
        }

        public override Core.TaskStatus Run()
        {
            Info("Début du téléchargement des fichiers biodata...");

            try
            {
                var result = DownloadAndSaveFile().Result;
                
                if (result != null)
                {
                    Info($"Téléchargement terminé pour le patient: {result.PatientNumber}");
                    return new Core.TaskStatus(Status.Success, false);
                }
                else
                {
                    Info("Aucun job disponible pour téléchargement");
                    return new Core.TaskStatus(Status.Success, false);
                }
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur dans CustomDownloadBiodata: {0}", ex.Message);
                return new Core.TaskStatus(Status.Error, false);
            }
        }

        private async Task<DownloadResult> DownloadAndSaveFile()
        {
            try
            {
                var url = $"{ThemaApiUrl.TrimEnd('/')}/workflow/download-succeeded";
                Info($"Tentative de téléchargement depuis l'URL: {url}");

                var response = await httpClient.GetAsync(url);
                
                Info($"Code de statut HTTP: {response.StatusCode}");

                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    Info("Aucun job disponible pour téléchargement (erreur 404)");
                    return null;
                }

                if (!response.IsSuccessStatusCode)
                {
                    Info($"Aucun job disponible pour téléchargement (code {response.StatusCode})");
                    return null;
                }

                var content = await response.Content.ReadAsStringAsync();
                Info($"Contenu reçu (500 premiers caractères): {content.Substring(0, Math.Min(500, content.Length))}...");

                JsonElement data;
                try
                {
                    data = JsonSerializer.Deserialize<JsonElement>(content);
                }
                catch (JsonException ex)
                {
                    throw new Exception($"Format de réponse invalide: {ex.Message}");
                }

                // Vérifier si aucun job terminé n'est disponible
                if (data.ValueKind == JsonValueKind.Object && 
                    data.TryGetProperty("detail", out var detail) && 
                    detail.GetString() == "No succeeded jobs available for download")
                {
                    Info("Aucun job terminé disponible pour téléchargement");
                    return null;
                }

                // Extraire les métadonnées
                if (!data.TryGetProperty("metadata", out var metadata))
                {
                    throw new Exception("Métadonnées manquantes dans la réponse");
                }

                var patientNumber = metadata.GetProperty("patient_number").GetString();
                var fileUuid = metadata.GetProperty("uuid").GetString();

                if (string.IsNullOrEmpty(patientNumber) || string.IsNullOrEmpty(fileUuid))
                {
                    throw new Exception("Métadonnées incomplètes (patient_number ou uuid manquant)");
                }

                Info($"Début du traitement pour le patient: {patientNumber}");

                // Créer la structure de répertoires
                var jsonDir = Path.Combine(MountedLabPath, patientNumber, "logs", "json");
                var hl7Dir = Path.Combine(MountedLabPath, patientNumber, "hl7");

                Directory.CreateDirectory(jsonDir);
                Directory.CreateDirectory(hl7Dir);

                // Sauvegarder les données JSON
                var jsonFilename = $"result_{fileUuid}.json";
                var jsonFilePath = Path.Combine(jsonDir, jsonFilename);
                await File.WriteAllTextAsync(jsonFilePath, content, Encoding.UTF8);
                Info($"Fichier JSON des données brutes sauvegardé: {jsonFilePath}");

                // Générer le fichier HL7
                var hl7Content = await GenerateHL7Content(data);
                var hl7Filename = $"result_{fileUuid}.hl7";
                var hl7FilePath = Path.Combine(hl7Dir, hl7Filename);
                
                await File.WriteAllTextAsync(hl7FilePath, hl7Content, Encoding.GetEncoding("windows-1252"));
                Info($"Fichier HL7 sauvegardé: {hl7FilePath}");

                // Copie à la racine pour compatibilité
                var hl7RootPath = Path.Combine(MountedLabPath, hl7Filename);
                await File.WriteAllTextAsync(hl7RootPath, hl7Content, Encoding.GetEncoding("windows-1252"));
                Info($"Fichier HL7 également copié à la racine: {hl7RootPath}");

                // Traiter le fichier PDF correspondant
                var pdfPath = await ProcessCorrespondingPDF(fileUuid, patientNumber);

                return new DownloadResult
                {
                    PatientNumber = patientNumber,
                    PdfPath = pdfPath,
                    Hl7Path = hl7FilePath,
                    FileUuid = fileUuid
                };
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du téléchargement et de la sauvegarde: {0}", ex.Message);
                throw;
            }
        }

        private async Task<string> GenerateHL7Content(JsonElement data)
        {
            // Simulation de la génération HL7 - à adapter selon votre logique métier
            // Cette méthode devrait implémenter la logique de génération HL7 
            // équivalente à celle du DAG Airflow original
            
            try
            {
                // Ici vous devriez implémenter la logique de génération HL7
                // basée sur les données JSON reçues et les appels à l'API HMD
                
                var hl7Content = $@"MSH|^~\&|THEMA|LAB|HMD|HOSPITAL|{DateTime.Now:yyyyMMddHHmmss}||ORU^R01|{Guid.NewGuid()}|P|2.5
PID|1||{data.GetProperty("metadata").GetProperty("patient_number").GetString()}||PATIENT^TEST||19800101|M
OBR|1||{data.GetProperty("metadata").GetProperty("uuid").GetString()}|LAB^LABORATORY||{DateTime.Now:yyyyMMddHHmmss}
OBX|1|TX|RESULT||{data.ToString()}||||||F";

                return hl7Content;
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la génération du contenu HL7: {0}", ex.Message);
                throw;
            }
        }

        private async Task<string> ProcessCorrespondingPDF(string fileUuid, string patientNumber)
        {
            try
            {
                var submittedDir = Path.Combine(MountedLabPath, "SUBMITTED");
                
                if (!Directory.Exists(submittedDir))
                {
                    Info($"Répertoire SUBMITTED non trouvé: {submittedDir}");
                    return null;
                }

                // Rechercher le fichier PDF avec l'UUID
                var files = Directory.GetFiles(submittedDir, "*.*");
                string pdfFile = null;

                foreach (var file in files)
                {
                    if (Path.GetFileName(file).Contains(fileUuid))
                    {
                        pdfFile = file;
                        break;
                    }
                }

                if (pdfFile == null)
                {
                    Info($"Aucun fichier PDF trouvé avec l'UUID: {fileUuid}");
                    return null;
                }

                Info($"Fichier PDF localisé: {pdfFile}");

                // Créer le répertoire patient et déplacer le PDF
                var patientDir = Path.Combine(MountedLabPath, patientNumber);
                Directory.CreateDirectory(patientDir);

                var pdfDestination = Path.Combine(patientDir, Path.GetFileName(pdfFile));
                File.Move(pdfFile, pdfDestination);
                Info($"PDF déplacé vers: {pdfDestination}");

                return pdfDestination;
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du traitement du PDF: {0}", ex.Message);
                return null;
            }
        }
    }

    public class DownloadResult
    {
        public string PatientNumber { get; set; }
        public string PdfPath { get; set; }
        public string Hl7Path { get; set; }
        public string FileUuid { get; set; }
    }
}

// ========================================
// CustomGetActiveJobs - Récupération des jobs actifs
// ========================================
namespace Wexflow.Tasks.CustomGetActiveJobs
{
    public class CustomGetActiveJobs : Core.Task
    {
        public string ThemaApiUrl { get; }

        private static readonly HttpClient httpClient = new HttpClient();

        public CustomGetActiveJobs(XElement xe, Workflow wf) : base(xe, wf)
        {
            ThemaApiUrl = ConfigurationHelper.GetSetting("thema_api_url", "https://biodata.hemadialyse.com");
        }

        public override Core.TaskStatus Run()
        {
            Info("Début de la récupération des jobs actifs...");

            try
            {
                var activeJobs = GetActiveJobs().Result;
                
                if (activeJobs != null && activeJobs.Count > 0)
                {
                    Info($"Nombre de jobs actifs trouvés: {activeJobs.Count}");
                    
                    // Stocker les jobs actifs pour la tâche suivante
                    foreach (var job in activeJobs)
                    {
                        Info($"Job ID: {job.Id}, UUID: {job.JobUuid}, Status: {job.Status}");
                    }
                    
                    // Sauvegarder les jobs dans les fichiers de workflow pour la tâche suivante
                    var jobsJson = JsonSerializer.Serialize(activeJobs, new JsonSerializerOptions { WriteIndented = true });
                    Files.Add(new FileInf($"active_jobs_{DateTime.Now:yyyyMMddHHmmss}.json", Id));
                    
                    return new Core.TaskStatus(Status.Success, false);
                }
                else
                {
                    Info("Aucun job actif trouvé");
                    return new Core.TaskStatus(Status.Success, false);
                }
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur dans CustomGetActiveJobs: {0}", ex.Message);
                return new Core.TaskStatus(Status.Error, false);
            }
        }

        private async Task<List<JobInfo>> GetActiveJobs()
        {
            try
            {
                // Simulation de la récupération des jobs actifs
                // Dans l'implémentation réelle, vous devriez utiliser votre logique de base de données
                // équivalente à query_active_jobs() du DAG Airflow original
                
                var activeJobs = new List<JobInfo>();
                
                // Exemple de jobs actifs (à remplacer par votre logique de base de données)
                // Cette partie devrait interroger votre base de données pour récupérer
                // les jobs avec le statut "SUBMITTED" ou "RUNNING"
                
                Info("Récupération des jobs avec le statut 'SUBMITTED' ou 'RUNNING'");
                
                // Ici vous devriez implémenter la logique équivalente à :
                // jobs = query_active_jobs()
                // Pour l'exemple, nous créons quelques jobs fictifs
                
                // TODO: Remplacer par votre logique de base de données réelle
                // var jobs = await QueryActiveJobsFromDatabase();
                
                // Pour l'instant, retourner une liste vide si aucune logique de DB n'est implémentée
                Info("Logique de base de données non implémentée - retour d'une liste vide");
                
                return activeJobs;
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la récupération des jobs actifs: {0}", ex.Message);
                throw;
            }
        }

        // Méthode à implémenter selon votre logique de base de données
        private async Task<List<JobInfo>> QueryActiveJobsFromDatabase()
        {
            // TODO: Implémenter la logique de requête de base de données
            // équivalente à la fonction query_active_jobs du module query_biodata_jobs_lib
            
            var jobs = new List<JobInfo>();
            
            try
            {
                // Exemple de logique de base de données
                // Remplacer par votre implémentation réelle
                
                // using var connection = new SqlConnection(connectionString);
                // var query = "SELECT id, job_uuid, status FROM jobs WHERE status IN ('SUBMITTED', 'RUNNING')";
                // var result = await connection.QueryAsync<JobInfo>(query);
                // jobs.AddRange(result);
                
                Info("Requête de base de données pour les jobs actifs exécutée");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la requête de base de données: {0}", ex.Message);
                throw;
            }
            
            return jobs;
        }
    }

    public class JobInfo
    {
        public int Id { get; set; }
        public string JobUuid { get; set; }
        public string Status { get; set; }
    }
}

// ========================================
// CustomFileWatcher - Surveillance de fichiers avec filtrage par date
// ========================================
namespace Wexflow.Tasks.CustomFileWatcher
{
    /// <summary>
    /// Tâche personnalisée qui hérite de FileWatcher mais ne déclenche que les fichiers 
    /// nouvellement créés après la date d'activation du workflow
    /// </summary>
    public class CustomFileWatcher : Core.Task
    {
        public string WatchPath { get; }
        public string FilePattern { get; }
        public bool IncludeSubfolders { get; }
        public DateTime WorkflowStartTime { get; private set; }
        public string StateFilePath { get; }
        public int OnFileCreated { get; }

        private FileSystemWatcher _fileWatcher;
        private readonly object _lockObject = new object();

        public CustomFileWatcher(XElement xe, Workflow wf) : base(xe, wf)
        {
            // Configuration depuis le XML
            WatchPath = GetSetting("watchPath", "/opt/airflow/data");
            FilePattern = GetSetting("filePattern", "*");
            IncludeSubfolders = bool.Parse(GetSetting("includeSubfolders", "true"));
            OnFileCreated = int.Parse(GetSetting("onFileCreated", "-1"));
            
            // Fichier d'état pour persister la date de démarrage
            StateFilePath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "Wexflow",
                "CustomFileWatcher",
                $"state_{Id}.json"
            );

            // Initialiser ou récupérer la date de démarrage du workflow
            InitializeWorkflowStartTime();
        }

        public override Core.TaskStatus Run()
        {
            Info("Démarrage de CustomFileWatcher...");
            
            try
            {
                // Construire le chemin complet avec le pattern
                var fullWatchPath = BuildWatchPath();
                
                if (!Directory.Exists(Path.GetDirectoryName(fullWatchPath)))
                {
                    Error($"Le répertoire de surveillance n'existe pas: {Path.GetDirectoryName(fullWatchPath)}");
                    return new Core.TaskStatus(Status.Error, false);
                }

                Info($"Surveillance du répertoire: {fullWatchPath}");
                Info($"Pattern de fichiers: {FilePattern}");
                Info($"Date de démarrage du workflow: {WorkflowStartTime:yyyy-MM-dd HH:mm:ss}");
                Info($"Inclure les sous-dossiers: {IncludeSubfolders}");

                // Vérifier les fichiers existants qui ont été créés après le démarrage
                CheckExistingFiles(fullWatchPath);

                // Démarrer la surveillance en temps réel
                StartFileWatcher(fullWatchPath);

                Info("CustomFileWatcher démarré avec succès");
                return new Core.TaskStatus(Status.Success, false);
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur dans CustomFileWatcher: {0}", ex.Message);
                return new Core.TaskStatus(Status.Error, false);
            }
        }

        private string BuildWatchPath()
        {
            // Construire le chemin selon le pattern fourni
            // filepath=os.path.join('/opt/airflow/data', '*', 'bbio', '*', '{' + file_pattern_str + '}')
            
            var basePath = WatchPath; // /opt/airflow/data
            
            // Pour la surveillance, on surveille le répertoire de base
            // et on filtrera les fichiers selon le pattern dans les sous-répertoires
            return basePath;
        }

        private void InitializeWorkflowStartTime()
        {
            try
            {
                // Créer le répertoire d'état s'il n'existe pas
                var stateDir = Path.GetDirectoryName(StateFilePath);
                if (!Directory.Exists(stateDir))
                {
                    Directory.CreateDirectory(stateDir);
                }

                // Vérifier si un fichier d'état existe déjà
                if (File.Exists(StateFilePath))
                {
                    var stateJson = File.ReadAllText(StateFilePath);
                    var state = JsonSerializer.Deserialize<WorkflowState>(stateJson);
                    WorkflowStartTime = state.StartTime;
                    Info($"Date de démarrage récupérée depuis l'état: {WorkflowStartTime:yyyy-MM-dd HH:mm:ss}");
                }
                else
                {
                    // Premier démarrage - enregistrer la date actuelle
                    WorkflowStartTime = DateTime.Now;
                    SaveWorkflowState();
                    Info($"Premier démarrage - date de démarrage enregistrée: {WorkflowStartTime:yyyy-MM-dd HH:mm:ss}");
                }
            }
            catch (Exception ex)
            {
                // En cas d'erreur, utiliser la date actuelle
                WorkflowStartTime = DateTime.Now;
                ErrorFormat("Erreur lors de l'initialisation de la date de démarrage: {0}", ex.Message);
            }
        }

        private void SaveWorkflowState()
        {
            try
            {
                var state = new WorkflowState
                {
                    StartTime = WorkflowStartTime,
                    LastUpdate = DateTime.Now
                };

                var stateJson = JsonSerializer.Serialize(state, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(StateFilePath, stateJson);
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la sauvegarde de l'état: {0}", ex.Message);
            }
        }

        private void CheckExistingFiles(string watchPath)
        {
            try
            {
                Info("Vérification des fichiers existants...");

                // Rechercher les fichiers selon le pattern spécifique
                var searchPattern = Path.Combine(watchPath, "*", "bbio", "*");
                var directories = Directory.GetDirectories(watchPath, "*", SearchOption.TopDirectoryOnly);

                int foundFiles = 0;

                foreach (var dir in directories)
                {
                    var bbioPath = Path.Combine(dir, "bbio");
                    if (Directory.Exists(bbioPath))
                    {
                        var bbioSubDirs = Directory.GetDirectories(bbioPath, "*", SearchOption.TopDirectoryOnly);
                        
                        foreach (var bbioSubDir in bbioSubDirs)
                        {
                            var files = Directory.GetFiles(bbioSubDir, FilePattern, SearchOption.TopDirectoryOnly);
                            
                            foreach (var file in files)
                            {
                                var fileInfo = new FileInfo(file);
                                
                                // Vérifier si le fichier a été créé après le démarrage du workflow
                                if (fileInfo.CreationTime > WorkflowStartTime)
                                {
                                    Info($"Fichier existant détecté (créé après démarrage): {file}");
                                    ProcessNewFile(file);
                                    foundFiles++;
                                }
                                else
                                {
                                    InfoFormat("Fichier ignoré (créé avant démarrage): {0} (créé le {1:yyyy-MM-dd HH:mm:ss})", 
                                        file, fileInfo.CreationTime);
                                }
                            }
                        }
                    }
                }

                Info($"Vérification terminée. {foundFiles} fichiers existants traités.");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la vérification des fichiers existants: {0}", ex.Message);
            }
        }

        private void StartFileWatcher(string watchPath)
        {
            try
            {
                _fileWatcher = new FileSystemWatcher(watchPath)
                {
                    IncludeSubdirectories = IncludeSubfolders,
                    NotifyFilter = NotifyFilters.CreationTime | NotifyFilters.FileName,
                    EnableRaisingEvents = true
                };

                _fileWatcher.Created += OnFileCreatedEvent;
                _fileWatcher.Error += OnFileWatcherError;

                Info("FileSystemWatcher démarré");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du démarrage de FileSystemWatcher: {0}", ex.Message);
                throw;
            }
        }

        private void OnFileCreatedEvent(object sender, FileSystemEventArgs e)
        {
            try
            {
                lock (_lockObject)
                {
                    // Vérifier si le fichier correspond au pattern spécifique
                    if (IsFileMatchingPattern(e.FullPath))
                    {
                        var fileInfo = new FileInfo(e.FullPath);
                        
                        // Double vérification : le fichier doit être créé après le démarrage du workflow
                        if (fileInfo.CreationTime > WorkflowStartTime)
                        {
                            Info($"Nouveau fichier détecté: {e.FullPath}");
                            ProcessNewFile(e.FullPath);
                        }
                        else
                        {
                            InfoFormat("Fichier ignoré (créé avant démarrage): {0} (créé le {1:yyyy-MM-dd HH:mm:ss})", 
                                e.FullPath, fileInfo.CreationTime);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du traitement de l'événement de création de fichier: {0}", ex.Message);
            }
        }

        private bool IsFileMatchingPattern(string filePath)
        {
            try
            {
                // Vérifier si le chemin correspond au pattern:
                // /opt/airflow/data/*/bbio/*/[FilePattern]
                
                var relativePath = Path.GetRelativePath(WatchPath, filePath);
                var pathParts = relativePath.Split(Path.DirectorySeparatorChar);

                // Le chemin doit avoir au moins 4 parties: [dir1]/bbio/[dir2]/[filename]
                if (pathParts.Length < 4)
                    return false;

                // Vérifier que "bbio" est présent à la bonne position
                if (pathParts[1] != "bbio")
                    return false;

                // Vérifier que le nom de fichier correspond au pattern
                var fileName = Path.GetFileName(filePath);
                return MatchesPattern(fileName, FilePattern);
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la vérification du pattern: {0}", ex.Message);
                return false;
            }
        }

        private bool MatchesPattern(string fileName, string pattern)
        {
            // Implémentation simple de correspondance de pattern avec wildcards
            if (pattern == "*" || pattern == "*.*")
                return true;

            if (pattern.Contains("*"))
            {
                // Convertir le pattern en regex simple
                var regexPattern = pattern.Replace("*", ".*").Replace("?", ".");
                return System.Text.RegularExpressions.Regex.IsMatch(fileName, regexPattern, 
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            }

            return string.Equals(fileName, pattern, StringComparison.OrdinalIgnoreCase);
        }

        private void ProcessNewFile(string filePath)
        {
            try
            {
                Info($"Traitement du nouveau fichier: {filePath}");

                // Créer un FileInf pour Wexflow
                var fileInf = new FileInf(filePath, Id);
                Files.Add(fileInf);

                // Si une tâche suivante est configurée, la déclencher
                if (OnFileCreated > 0)
                {
                    Info($"Déclenchement de la tâche suivante (ID: {OnFileCreated})");
                    
                    // Dans Wexflow, vous pouvez déclencher une tâche suivante
                    // Cette partie dépend de l'implémentation spécifique de votre version de Wexflow
                    TriggerNextTask(OnFileCreated, filePath);
                }

                Info($"Fichier traité avec succès: {filePath}");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du traitement du fichier {0}: {1}", filePath, ex.Message);
            }
        }

        private void TriggerNextTask(int taskId, string filePath)
        {
            try
            {
                // Cette méthode dépend de l'implémentation de Wexflow
                // Vous devrez adapter selon votre version
                
                Info($"Déclenchement de la tâche {taskId} pour le fichier {filePath}");
                
                // Exemple d'implémentation - à adapter selon votre version de Wexflow
                // Workflow.StartTask(taskId);
                
                // Ou utiliser le système d'événements de Wexflow si disponible
                // Workflow.TriggerTask(taskId, new Dictionary<string, object> { { "filePath", filePath } });
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du déclenchement de la tâche {0}: {1}", taskId, ex.Message);
            }
        }

        private void OnFileWatcherError(object sender, ErrorEventArgs e)
        {
            ErrorFormat("Erreur FileSystemWatcher: {0}", e.GetException().Message);
            
            // Tentative de redémarrage du watcher
            try
            {
                _fileWatcher?.Dispose();
                StartFileWatcher(BuildWatchPath());
                Info("FileSystemWatcher redémarré après erreur");
            }
            catch (Exception ex)
            {
                ErrorFormat("Impossible de redémarrer FileSystemWatcher: {0}", ex.Message);
            }
        }

        public void StopFileWatcher()
        {
            try
            {
                _fileWatcher?.Dispose();
                Info("CustomFileWatcher arrêté");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de l'arrêt de CustomFileWatcher: {0}", ex.Message);
            }
        }

        /// <summary>
        /// Réinitialise la date de démarrage du workflow (utile pour les tests ou redémarrages)
        /// </summary>
        public void ResetWorkflowStartTime()
        {
            try
            {
                WorkflowStartTime = DateTime.Now;
                SaveWorkflowState();
                Info($"Date de démarrage réinitialisée: {WorkflowStartTime:yyyy-MM-dd HH:mm:ss}");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la réinitialisation: {0}", ex.Message);
            }
        }
    }

    /// <summary>
    /// Classe pour persister l'état du workflow
    /// </summary>
    public class WorkflowState
    {
        public DateTime StartTime { get; set; }
        public DateTime LastUpdate { get; set; }
    }
}

// ========================================
// CustomThemaProcessing - Traitement des fichiers Thema
// ========================================
namespace Wexflow.Tasks.CustomThemaProcessing
{
    public class CustomThemaProcessing : Core.Task
    {
        public string MountedLabPath { get; }
        public string HmdApiUrl { get; }
        public string ThemaApiUrl { get; }
        public string JwtToken { get; }
        public string IdentifiantLabo { get; }
        public string DataDir { get; }

        private static readonly HttpClient httpClient = new HttpClient();

        public CustomThemaProcessing(XElement xe, Workflow wf) : base(xe, wf)
        {
            MountedLabPath = ConfigurationHelper.GetSetting("repert_labo", "/mnt/remote_share_processed");
            HmdApiUrl = ConfigurationHelper.GetSetting("hmd_api_url", "http://10.1.16.50:8083");
            ThemaApiUrl = ConfigurationHelper.GetSetting("thema_api_url", "https://biodata.hemadialyse.com");
            JwtToken = ConfigurationHelper.GetSetting("hmd_jwt_token", "");
            IdentifiantLabo = ConfigurationHelper.GetSetting("identifiant_labo", "EMA_LAB");
            DataDir = ConfigurationHelper.GetSetting("base_path", "/mnt/remote_share/hmd_pj");
        }

        public override Core.TaskStatus Run()
        {
            Info("Début du traitement des fichiers détectés...");

            try
            {
                // Récupérer les fichiers depuis la tâche FilesLoader précédente
                var files = SelectFiles();
                if (!files.Any())
                {
                    Info("Aucun fichier à traiter.");
                    return new Core.TaskStatus(Status.Success, false);
                }

                Info($"Traitement de {files.Length} fichiers");

                // S'assurer que les répertoires existent
                EnsureDirectoriesExist();

                var processedFiles = new List<FileInfo>();

                foreach (var file in files)
                {
                    try
                    {
                        Info($"Traitement du fichier: {file.Path}");
                        var fileInfo = ProcessDetectedFile(file.Path);
                        
                        if (fileInfo != null)
                        {
                            processedFiles.Add(fileInfo);
                            
                            // Traiter le document via l'API
                            if (fileInfo.Status != "failed")
                            {
                                ProcessDocument(fileInfo);
                            }
                            
                            // Déplacer le fichier original vers .processed
                            MoveOriginalFile(fileInfo);
                        }
                    }
                    catch (Exception ex)
                    {
                        ErrorFormat("Erreur lors du traitement du fichier {0}: {1}", file.Path, ex.Message);
                    }
                }

                // Sauvegarder le résumé des fichiers traités
                SaveProcessedFilesSummary(processedFiles);

                Info($"Traitement terminé. {processedFiles.Count} fichiers traités avec succès.");
                return new Core.TaskStatus(Status.Success, false);
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur dans CustomThemaProcessing: {0}", ex.Message);
                return new Core.TaskStatus(Status.Error, false);
            }
        }

        private void EnsureDirectoriesExist()
        {
            try
            {
                if (!Directory.Exists(MountedLabPath))
                {
                    Directory.CreateDirectory(MountedLabPath);
                    Info($"Répertoire créé: {MountedLabPath}");
                }

                var submittedDir = Path.Combine(MountedLabPath, "SUBMITTED");
                if (!Directory.Exists(submittedDir))
                {
                    Directory.CreateDirectory(submittedDir);
                    Info($"Répertoire SUBMITTED créé: {submittedDir}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Impossible de créer les répertoires: {ex.Message}");
            }
        }

        private async Task<string> CheckFileStatus(string filename)
        {
            try
            {
                var url = $"{ThemaApiUrl.TrimEnd('/')}/files/status/{filename}";
                Info($"Vérification du statut du fichier via URL: {url}");

                var response = await httpClient.GetAsync(url);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var statusData = JsonSerializer.Deserialize<JsonElement>(content);
                    
                    if (statusData.TryGetProperty("status", out var status))
                    {
                        return status.GetString();
                    }
                }
                else
                {
                    ErrorFormat("Erreur lors de la vérification du statut: {0}", response.StatusCode);
                }
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la vérification du statut: {0}", ex.Message);
            }
            
            return null;
        }

        private string ExtractPatientFolder(string filePath)
        {
            try
            {
                var relativePath = filePath.Substring(DataDir.Length + 1);
                var parts = relativePath.Split(Path.DirectorySeparatorChar);
                return parts[0];
            }
            catch
            {
                throw new ArgumentException($"Impossible d'extraire le numéro du patient du chemin: {filePath}");
            }
        }

        private FileInfo ProcessDetectedFile(string filePath)
        {
            try
            {
                var patientFolder = ExtractPatientFolder(filePath);
                var originalFilename = Path.GetFileName(filePath);
                var fileBase = Path.GetFileNameWithoutExtension(originalFilename);
                var fileExt = Path.GetExtension(originalFilename);

                // Vérifier le statut du fichier
                var fileStatus = CheckFileStatus(originalFilename).Result;
                
                if (fileStatus == "failed")
                {
                    Info($"Le fichier {originalFilename} a le statut 'failed', aucune action ne sera effectuée");
                    return new FileInfo
                    {
                        Filename = originalFilename,
                        NumPat = patientFolder,
                        OriginalPath = filePath,
                        Status = "failed",
                        ProcessedDate = DateTime.Now
                    };
                }

                // Générer un nouveau nom avec timestamp
                var timestamp = DateTime.Now.ToString("ddHHmmss");
                var newFilename = $"{fileBase}_{timestamp}{fileExt}";
                
                var submittedDir = Path.Combine(MountedLabPath, "SUBMITTED");
                var newPath = Path.Combine(submittedDir, newFilename);

                // Copier le fichier
                File.Copy(filePath, newPath, true);
                Info($"Fichier copié vers: {newPath}");

                var fileInfo = new FileInfo
                {
                    OriginalFilename = originalFilename,
                    Filename = newFilename,
                    NumPat = patientFolder,
                    OriginalPath = filePath,
                    NewPath = newPath,
                    Status = "submitted",
                    ProcessedDate = DateTime.Now
                };

                // Créer le fichier JSON de suivi
                var jsonPath = Path.Combine(MountedLabPath, $"file_info_{DateTime.Now:yyyyMMdd_HHmmss}.json");
                var jsonContent = JsonSerializer.Serialize(fileInfo, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(jsonPath, jsonContent, Encoding.GetEncoding("windows-1252"));
                
                Info($"Fichier JSON créé: {jsonPath}");
                return fileInfo;
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du traitement du fichier {0}: {1}", filePath, ex.Message);
                throw;
            }
        }

        private async void ProcessDocument(FileInfo fileInfo)
        {
            try
            {
                if (fileInfo.Status == "failed")
                {
                    Info($"Le fichier {fileInfo.Filename} a le statut 'failed', il ne sera pas traité");
                    return;
                }

                var url = $"{ThemaApiUrl.TrimEnd('/')}/workflow/process-document";
                Info($"URL de traitement du document: {url}");

                if (!File.Exists(fileInfo.NewPath))
                {
                    throw new FileNotFoundException($"Le fichier n'existe pas: {fileInfo.NewPath}");
                }

                using var form = new MultipartFormDataContent();
                using var fileContent = new ByteArrayContent(File.ReadAllBytes(fileInfo.NewPath));
                
                fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/pdf");
                form.Add(fileContent, "file", fileInfo.Filename);
                form.Add(new StringContent(fileInfo.NumPat), "patient_number");
                form.Add(new StringContent("false"), "wait_for_completion");

                var response = await httpClient.PostAsync(url, form);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Info($"Document traité avec succès: {fileInfo.Filename}");
                    Info($"Réponse de l'API: {responseContent}");

                    // Traiter la réponse pour récupérer l'UUID si présent
                    try
                    {
                        var apiResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
                        if (apiResponse.TryGetProperty("uuid", out var uuidElement))
                        {
                            var uuid = uuidElement.GetString();
                            RenameFileWithUuid(fileInfo, uuid);
                        }
                    }
                    catch (Exception ex)
                    {
                        ErrorFormat("Erreur lors du parsing de la réponse API: {0}", ex.Message);
                    }
                }
                else
                {
                    throw new Exception($"Erreur API: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du traitement du document: {0}", ex.Message);
            }
        }

        private void RenameFileWithUuid(FileInfo fileInfo, string uuid)
        {
            try
            {
                var fileBase = Path.GetFileNameWithoutExtension(fileInfo.Filename);
                var fileExt = Path.GetExtension(fileInfo.Filename);
                var uuidFilename = $"{fileBase}_{uuid}{fileExt}";
                var newUuidPath = Path.Combine(Path.GetDirectoryName(fileInfo.NewPath), uuidFilename);

                File.Move(fileInfo.NewPath, newUuidPath);
                Info($"Fichier renommé avec UUID: {uuidFilename}");

                fileInfo.PreviousPath = fileInfo.NewPath;
                fileInfo.NewPath = newUuidPath;
                fileInfo.Filename = uuidFilename;
                fileInfo.Uuid = uuid;
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors du renommage avec UUID: {0}", ex.Message);
            }
        }

        private void MoveOriginalFile(FileInfo fileInfo)
        {
            try
            {
                if (File.Exists(fileInfo.OriginalPath))
                {
                    var originalDir = Path.GetDirectoryName(fileInfo.OriginalPath);
                    var processedDir = Path.Combine(originalDir, ".processed");
                    
                    if (!Directory.Exists(processedDir))
                    {
                        Directory.CreateDirectory(processedDir);
                    }
                    
                    var processedPath = Path.Combine(processedDir, Path.GetFileName(fileInfo.OriginalPath));
                    File.Move(fileInfo.OriginalPath, processedPath);
                    Info($"Fichier original déplacé vers .processed: {processedPath}");
                }
            }
            catch (Exception ex)
            {
                InfoFormat("Impossible de déplacer le fichier original: {0}", ex.Message);
            }
        }

        private void SaveProcessedFilesSummary(List<FileInfo> processedFiles)
        {
            try
            {
                if (processedFiles.Any())
                {
                    var summaryPath = Path.Combine(MountedLabPath, $"processed_summary_{DateTime.Now:yyyyMMdd_HHmmss}.json");
                    var jsonContent = JsonSerializer.Serialize(processedFiles, new JsonSerializerOptions { WriteIndented = true });
                    File.WriteAllText(summaryPath, jsonContent, Encoding.UTF8);
                    Info($"Résumé des fichiers traités sauvegardé: {summaryPath}");
                }
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la sauvegarde du résumé: {0}", ex.Message);
            }
        }
    }

    public class FileInfo
    {
        public string OriginalFilename { get; set; }
        public string Filename { get; set; }
        public string NumPat { get; set; }
        public string OriginalPath { get; set; }
        public string NewPath { get; set; }
        public string PreviousPath { get; set; }
        public string Status { get; set; }
        public string Uuid { get; set; }
        public DateTime ProcessedDate { get; set; }
    }
}


// ========================================
// ConfigurationHelper - Utilitaire pour lire les fichiers de configuration
// ========================================
public static class ConfigurationHelper
{
    private static Dictionary<string, string> _variables;

    static ConfigurationHelper()
    {
        LoadConfiguration();
    }

    private static void LoadConfiguration()
    {
        try
        {
            var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config", "variables.json");
            if (File.Exists(configPath))
            {
                var jsonContent = File.ReadAllText(configPath);
                var jsonDoc = JsonSerializer.Deserialize<Dictionary<string, string>>(jsonContent);
                _variables = jsonDoc ?? new Dictionary<string, string>();
            }
            else
            {
                _variables = new Dictionary<string, string>();
            }
        }
        catch (Exception)
        {
            _variables = new Dictionary<string, string>();
        }
    }

    public static string GetSetting(string key, string defaultValue = null)
    {
        if (_variables.TryGetValue(key, out var value))
        {
            return value;
        }
        return defaultValue;
    }
}
