using System.Xml.Linq;
using System.Text.Json;
using Wexflow.Core;
using System.Data;
using Microsoft.Data.Sqlite;

// ========================================
// BiodataJobStatusUpdater - Mise à jour des statuts des jobs biodata
// ========================================
namespace Wexflow.Tasks.BiodataJobStatusUpdater
{
    /// <summary>
    /// Tâche personnalisée qui vérifie et met à jour les statuts des jobs biodata
    /// Reprend la fonctionnalité du DAG thema_job_status_biodata
    /// </summary>
    public class BiodataJobStatusUpdater : Core.Task
    {
        public string DatabaseConnectionString { get; }
        public string ThemaApiUrl { get; }
        public int CheckIntervalMinutes { get; }
        public string[] ActiveStatuses { get; }

        private static readonly HttpClient _httpClient = new HttpClient();

        public BiodataJobStatusUpdater(XElement xe, Workflow wf) : base(xe, wf)
        {
            // Configuration depuis le XML
            var sqlitePath = GetSetting("sqlitePath", "../api/api/Db/biodata_trace.db") ?? "../api/api/Db/biodata_trace.db";
            
            Info($"Répertoire d'exécution: {AppDomain.CurrentDomain.BaseDirectory}");
            Info($"Chemin relatif de la base de données: {sqlitePath}");
            
            // Liste des chemins possibles à essayer
            var possiblePaths = new List<string>();
            
            // Construire le chemin absolu si nécessaire
            if (!Path.IsPathRooted(sqlitePath))
            {
                // 1. Chemin relatif au répertoire d'exécution
                var appDir = AppDomain.CurrentDomain.BaseDirectory;
                var path1 = Path.GetFullPath(Path.Combine(appDir, sqlitePath));
                possiblePaths.Add(path1);
                
                // 2. Chemin relatif au répertoire parent
                var parentDir = Directory.GetParent(appDir)?.FullName;
                if (parentDir != null)
                {
                    var path2 = Path.GetFullPath(Path.Combine(parentDir, sqlitePath.TrimStart('.', '/', '\\')));
                    possiblePaths.Add(path2);
                    
                    // 3. Chemin relatif au répertoire parent du parent
                    var grandParentDir = Directory.GetParent(parentDir)?.FullName;
                    if (grandParentDir != null)
                    {
                        var path3 = Path.GetFullPath(Path.Combine(grandParentDir, "api", "api", "Db", Path.GetFileName(sqlitePath)));
                        possiblePaths.Add(path3);
                    }
                }
                
                // 4. Chemin absolu direct (pour les cas où le chemin est déjà correct mais pas détecté comme absolu)
                possiblePaths.Add(sqlitePath);
                
                // Afficher tous les chemins possibles
                Info("Chemins possibles pour la base de données:");
                foreach (var path in possiblePaths)
                {
                    Info($"- {path} (Existe: {File.Exists(path)})");
                }
                
                // Essayer chaque chemin
                bool found = false;
                foreach (var path in possiblePaths)
                {
                    if (File.Exists(path))
                    {
                        Info($"Base de données trouvée à: {path}");
                        sqlitePath = path;
                        found = true;
                        break;
                    }
                }
                
                if (!found)
                {
                    // Si aucun fichier n'est trouvé, vérifier si les répertoires existent au moins
                    foreach (var path in possiblePaths)
                    {
                        var dir = Path.GetDirectoryName(path);
                        if (Directory.Exists(dir))
                        {
                            Info($"Répertoire trouvé, mais fichier manquant: {dir}");
                            // Utiliser le premier répertoire existant comme fallback
                            sqlitePath = path;
                            break;
                        }
                    }
                    
                    Error($"Attention: Aucun fichier de base de données trouvé. Utilisation du chemin: {sqlitePath}");
                }
            }
            
            DatabaseConnectionString = $"Data Source={sqlitePath}";
            
            ThemaApiUrl = GetSetting("themaApiUrl", "http://biodata_api:8000/") ?? "http://biodata_api:8000/";
            CheckIntervalMinutes = int.Parse(GetSetting("checkIntervalMinutes", "5") ?? "5");
            
            // Statuts considérés comme actifs (nécessitant une vérification)
            var statusesString = GetSetting("activeStatuses", "SUBMITTED,RUNNING,RUNNABLE,STARTING") ?? "SUBMITTED,RUNNING,RUNNABLE,STARTING";
            ActiveStatuses = statusesString.Split(',').Select(s => s.Trim().ToUpper()).ToArray();
        }

        public override Core.TaskStatus Run()
        {
            Info("Démarrage de BiodataJobStatusUpdater...");
            
            try
            {
                Info($"Base de données: {DatabaseConnectionString}");
                Info($"API Thema: {ThemaApiUrl}");
                Info($"Statuts actifs surveillés: {string.Join(", ", ActiveStatuses)}");

                // Récupérer les jobs actifs depuis la base de données
                var activeJobs = GetActiveJobs();
                
                if (activeJobs.Count == 0)
                {
                    Info("Aucun job actif trouvé");
                    return new Core.TaskStatus(Status.Success, false);
                }

                Info($"{activeJobs.Count} job(s) actif(s) trouvé(s)");

                // Vérifier le statut de chaque job via l'API
                int updatedJobs = 0;
                int errorJobs = 0;

                foreach (var job in activeJobs)
                {
                    try
                    {
                        Info($"Vérification du job ID: {job.Id}, UUID: {job.JobUuid}, Statut actuel: {job.Status}");
                        
                        var success = CheckJobStatus(job.JobUuid);
                        if (success)
                        {
                            updatedJobs++;
                            Info($"Job {job.JobUuid} vérifié avec succès");
                        }
                        else
                        {
                            errorJobs++;
                            Error($"Erreur lors de la vérification du job {job.JobUuid}");
                        }

                        // Petite pause entre les appels API pour éviter la surcharge
                        Thread.Sleep(1000);
                    }
                    catch (Exception ex)
                    {
                        errorJobs++;
                        ErrorFormat("Erreur lors de la vérification du job {0}: {1}", job.JobUuid, ex.Message);
                    }
                }

                Info($"Résumé: {updatedJobs} job(s) vérifiés avec succès, {errorJobs} erreur(s)");

                return errorJobs == 0 
                    ? new Core.TaskStatus(Status.Success, false)
                    : new Core.TaskStatus(Status.Warning, false);
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur dans BiodataJobStatusUpdater: {0}", ex.Message);
                return new Core.TaskStatus(Status.Error, false);
            }
        }

        private List<BiodataJob> GetActiveJobs()
        {
            var jobs = new List<BiodataJob>();
            
            try
            {
                using var connection = new SqliteConnection(DatabaseConnectionString);
                connection.Open();
                
                // Construire la requête avec les statuts
                var statusPlaceholders = string.Join(",", ActiveStatuses.Select((_, i) => $"@status{i}"));
                var query = $@"
                    SELECT id, job_uuid, status, created_at, updated_at 
                    FROM jobs 
                    WHERE UPPER(status) IN ({statusPlaceholders})
                    ORDER BY created_at DESC";

                using var command = new SqliteCommand(query, connection);
                
                // Ajouter les paramètres pour les statuts
                for (int i = 0; i < ActiveStatuses.Length; i++)
                {
                    command.Parameters.AddWithValue($"@status{i}", ActiveStatuses[i]);
                }

                using var reader = command.ExecuteReader();
                
                while (reader.Read())
                {
                    jobs.Add(new BiodataJob
                    {
                        Id = reader.GetInt32(reader.GetOrdinal("id")),
                        JobUuid = reader.GetString(reader.GetOrdinal("job_uuid")),
                        Status = reader.GetString(reader.GetOrdinal("status")),
                        CreatedAt = reader.GetDateTime(reader.GetOrdinal("created_at")),
                        UpdatedAt = reader.IsDBNull(reader.GetOrdinal("updated_at")) ? null : reader.GetDateTime(reader.GetOrdinal("updated_at"))
                    });
                }

                Info($"Récupération de {jobs.Count} job(s) actif(s) depuis la base de données");
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la récupération des jobs actifs: {0}", ex.Message);
                throw;
            }

            return jobs;
        }

        private bool CheckJobStatus(string jobUuid)
        {
            try
            {
                var baseUrl = ThemaApiUrl.TrimEnd('/');
                var url = $"{baseUrl}/jobs/check";

                Info($"Vérification du statut via URL: {url} pour le job UUID: {jobUuid}");

                // Préparer les données du formulaire
                var formData = new List<KeyValuePair<string, string>>
                {
                    new KeyValuePair<string, string>("job_id", jobUuid)
                };

                using var formContent = new FormUrlEncodedContent(formData);
                
                // Effectuer la requête POST
                var response = _httpClient.PostAsync(url, formContent).Result;
                
                if (response.IsSuccessStatusCode)
                {
                    var content = response.Content.ReadAsStringAsync().Result;
                    Info($"Réponse de l'API pour {jobUuid}: {content}");
                    
                    // Optionnel: parser la réponse JSON pour extraire des informations
                    try
                    {
                        var jsonResponse = JsonSerializer.Deserialize<Dictionary<string, object>>(content);
                        if (jsonResponse != null && jsonResponse.ContainsKey("status"))
                        {
                            var newStatus = jsonResponse["status"].ToString();
                            Info($"Nouveau statut pour {jobUuid}: {newStatus}");
                        }
                    }
                    catch (JsonException)
                    {
                        // Si ce n'est pas du JSON valide, on continue quand même
                        Info($"Réponse non-JSON reçue pour {jobUuid}");
                    }
                    
                    return true;
                }
                else
                {
                    ErrorFormat("L'API a répondu avec le code {0} pour le job {1}", response.StatusCode, jobUuid);
                    return false;
                }
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur lors de la vérification du statut du job {0}: {1}", jobUuid, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Méthode utilitaire pour tester la connexion à la base de données
        /// </summary>
        public bool TestDatabaseConnection()
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseConnectionString);
                connection.Open();
                
                using var command = new SqliteCommand("SELECT COUNT(*) FROM jobs", connection);
                var count = command.ExecuteScalar();
                
                Info($"Connexion à la base de données réussie. Nombre total de jobs: {count}");
                return true;
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur de connexion à la base de données: {0}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Méthode utilitaire pour tester la connexion à l'API
        /// </summary>
        public bool TestApiConnection()
        {
            try
            {
                var baseUrl = ThemaApiUrl.TrimEnd('/');
                var testUrl = $"{baseUrl}/health"; // Endpoint de santé supposé
                
                var response = _httpClient.GetAsync(testUrl).Result;
                
                if (response.IsSuccessStatusCode)
                {
                    Info("Connexion à l'API réussie");
                    return true;
                }
                else
                {
                    ErrorFormat("L'API a répondu avec le code {0}", response.StatusCode);
                    return false;
                }
            }
            catch (Exception ex)
            {
                ErrorFormat("Erreur de connexion à l'API: {0}", ex.Message);
                return false;
            }
        }
    }

    /// <summary>
    /// Classe représentant un job biodata
    /// </summary>
    public class BiodataJob
    {
        public int Id { get; set; }
        public string JobUuid { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    /// <summary>
    /// Classe utilitaire pour la configuration (réutilise celle du projet existant)
    /// </summary>
    public static class JobStatusConfigurationHelper
    {
        private static Dictionary<string, string> _variables = new Dictionary<string, string>();

        static JobStatusConfigurationHelper()
        {
            LoadConfiguration();
        }

        private static void LoadConfiguration()
        {
            try
            {
                var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config", "variables.json");
                if (File.Exists(configPath))
                {
                    var jsonContent = File.ReadAllText(configPath);
                    var jsonDoc = JsonSerializer.Deserialize<Dictionary<string, string>>(jsonContent);
                    _variables = jsonDoc ?? new Dictionary<string, string>();
                }
                else
                {
                    _variables = new Dictionary<string, string>();
                }
            }
            catch (Exception)
            {
                _variables = new Dictionary<string, string>();
            }
        }

        public static string? GetSetting(string key, string? defaultValue = null)
        {
            if (_variables.TryGetValue(key, out var value))
            {
                return value;
            }
            return defaultValue;
        }
    }
}
